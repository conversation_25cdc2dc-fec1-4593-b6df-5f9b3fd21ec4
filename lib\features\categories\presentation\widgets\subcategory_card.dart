import 'package:flutter/material.dart';
import '../../domain/subcategory.dart';

class SubCategoryCard extends StatelessWidget {
  final SubCategory subCategory;
  final VoidCallback? onEdit;
  final VoidCallback? onDelete;

  const SubCategoryCard({
    super.key,
    required this.subCategory,
    this.onEdit,
    this.onDelete,
  });

  // Mappage des noms d'icônes vers les IconData
  static const Map<String, IconData> _iconMap = {
    'category': Icons.category,
    'home_work': Icons.home_work,
    'electrical_services': Icons.electrical_services,
    'water_drop': Icons.water_drop,
    'build': Icons.build,
    'local_grocery_store': Icons.local_grocery_store,
    'shopping_cart': Icons.shopping_cart,
    'restaurant': Icons.restaurant,
    'local_bar': Icons.local_bar,
    'local_gas_station': Icons.local_gas_station,
    'directions_bus': Icons.directions_bus,
    'local_taxi': Icons.local_taxi,
    'car_repair': Icons.car_repair,
    'school': Icons.school,
    'edit': Icons.edit,
    'menu_book': Icons.menu_book,
    'person': Icons.person,
    'medication': Icons.medication,
    'medical_services': Icons.medical_services,
    'health_and_safety': Icons.health_and_safety,
    'phone_android': Icons.phone_android,
    'wifi': Icons.wifi,
    'mail': Icons.mail,
    'sports_esports': Icons.sports_esports,
    'celebration': Icons.celebration,
    'checkroom': Icons.checkroom,
    'local_laundry_service': Icons.local_laundry_service,
    'fitness_center': Icons.fitness_center,
    'movie': Icons.movie,
    'music_note': Icons.music_note,
    'book': Icons.book,
    'computer': Icons.computer,
    'phone': Icons.phone,
    'tv': Icons.tv,
  };

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: theme.colorScheme.secondary.withValues(alpha: 0.1),
          child: Icon(_getIcon(), color: theme.colorScheme.secondary, size: 20),
        ),
        title: Text(
          subCategory.name,
          style: theme.textTheme.bodyMedium?.copyWith(
            fontWeight: FontWeight.w500,
          ),
        ),
        subtitle: Text(
          'Sous-catégorie',
          style: theme.textTheme.bodySmall?.copyWith(color: Colors.grey[600]),
        ),
        trailing: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (onEdit != null)
              IconButton(
                icon: const Icon(Icons.edit, size: 20),
                onPressed: onEdit,
                tooltip: 'Modifier',
                color: Colors.blue,
              ),
            if (onDelete != null)
              IconButton(
                icon: const Icon(Icons.delete, size: 20),
                onPressed: () => _showDeleteConfirmation(context),
                tooltip: 'Supprimer',
                color: Colors.red,
              ),
          ],
        ),
      ),
    );
  }

  IconData _getIcon() {
    if (subCategory.iconName != null &&
        _iconMap.containsKey(subCategory.iconName)) {
      return _iconMap[subCategory.iconName]!;
    }
    return Icons
        .subdirectory_arrow_right; // Icône par défaut pour les sous-catégories
  }

  void _showDeleteConfirmation(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Confirmer la suppression'),
          content: Text(
            'Êtes-vous sûr de vouloir supprimer la sous-catégorie "${subCategory.name}" ?\n\n'
            'Cette action ne peut pas être annulée.',
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Annuler'),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                onDelete?.call();
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red,
                foregroundColor: Colors.white,
              ),
              child: const Text('Supprimer'),
            ),
          ],
        );
      },
    );
  }
}
