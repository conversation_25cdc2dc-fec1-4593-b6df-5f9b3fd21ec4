# Système de Gestion des Catégories

Ce document décrit le nouveau système de gestion des catégories et sous-catégories implémenté dans l'application Zoro Budget.

## Vue d'ensemble

Le système de catégories permet aux utilisateurs de :
- Gérer des catégories de dépenses et de revenus
- Créer des sous-catégories pour une organisation plus fine
- Utiliser des catégories prédéfinies ou créer des catégories personnalisées
- Associer des icônes et couleurs aux catégories

## Architecture

### Modèles de données

#### Category
- `id`: Identifiant unique
- `name`: Nom de la catégorie
- `type`: Type (expense/income)
- `iconName`: Nom de l'icône
- `colorValue`: Valeur de la couleur
- `userId`: ID utilisateur (null pour catégories prédéfinies)
- `isActive`: Statut actif/inactif
- `createdAt`/`updatedAt`: Timestamps

#### SubCategory
- `id`: Identifiant unique
- `name`: Nom de la sous-catégorie
- `categoryId`: ID de la catégorie parente
- `iconName`: Nom de l'icône
- `userId`: ID utilisateur (null pour sous-catégories prédéfinies)
- `isActive`: Statut actif/inactif
- `createdAt`/`updatedAt`: Timestamps

### Services

#### CategoryService
Service principal pour la gestion des catégories :
- `initializeDefaultCategories()`: Initialise les catégories prédéfinies
- `getAllCategories()`: Récupère toutes les catégories
- `getCategoriesByType(CategoryType)`: Récupère les catégories par type
- `getSubCategoriesByCategory(String)`: Récupère les sous-catégories d'une catégorie
- `createCategory()`: Crée une nouvelle catégorie
- `createSubCategory()`: Crée une nouvelle sous-catégorie
- `updateCategory()`/`updateSubCategory()`: Met à jour une catégorie/sous-catégorie
- `deleteCategory()`/`deleteSubCategory()`: Supprime (désactive) une catégorie/sous-catégorie

### Providers Riverpod

#### Providers disponibles
- `categoriesProvider`: Toutes les catégories
- `categoriesByTypeProvider`: Catégories par type (expense/income)
- `subCategoriesProvider`: Sous-catégories d'une catégorie
- `categoryByIdProvider`: Catégorie par ID
- `subCategoryByIdProvider`: Sous-catégorie par ID
- `expenseCategoriesProvider`: Noms des catégories de dépenses
- `incomeCategoriesProvider`: Noms des catégories de revenus
- `allCategoryNamesProvider`: Tous les noms de catégories

## Interface utilisateur

### CategoriesScreen
Écran principal de gestion des catégories avec :
- Onglets pour dépenses et revenus
- Liste des catégories avec leurs sous-catégories
- Boutons d'ajout, modification et suppression
- Interface d'expansion pour voir les sous-catégories

### Dialogues

#### AddCategoryDialog
- Création/modification de catégories
- Sélection d'icônes et couleurs
- Validation des données

#### AddSubCategoryDialog
- Création/modification de sous-catégories
- Sélection d'icônes
- Association à une catégorie parente

### Widgets

#### CategoryCard
- Affichage d'une catégorie
- Liste des sous-catégories
- Actions (éditer, supprimer, ajouter sous-catégorie)

#### SubCategoryCard
- Affichage d'une sous-catégorie
- Actions (éditer, supprimer)

## Intégration

### Dashboard
Le dashboard utilise maintenant le système de catégories dynamiques :
- Chargement des catégories selon le type de transaction
- Dropdown avec indicateur de chargement
- Validation de sélection

### Budgets
L'écran des budgets intègre les catégories de dépenses :
- Chargement dynamique des catégories
- Interface de sélection améliorée

### Navigation
Nouvelle route `/categories` ajoutée avec accès depuis :
- Barre de navigation inférieure du dashboard
- Icône dédiée dans la navigation

## Catégories prédéfinies

### Dépenses
- Alimentation (avec sous-catégories : Courses, Restaurants, Snacks)
- Transport (Carburant, Transport public, Taxi/VTC)
- Logement (Loyer, Électricité, Eau, Internet)
- Santé (Médecin, Pharmacie, Assurance santé)
- Éducation (Frais scolaires, Livres, Formations)
- Loisirs (Cinéma, Sport, Voyages)
- Vêtements (Vêtements, Chaussures, Accessoires)
- Autres

### Revenus
- Salaire
- Freelance
- Investissements
- Autres

## Stockage

Les données sont stockées localement avec Hive :
- Box `categories` pour les catégories
- Box `subcategories` pour les sous-catégories
- Adaptateurs Hive pour la sérialisation

## Migration

Le système remplace les listes de catégories codées en dur :
- Dashboard : Migration vers chargement dynamique
- Budgets : Intégration du nouveau système
- Transactions : Prêt pour l'intégration future

## Utilisation

### Pour les développeurs

```dart
// Récupérer les catégories de dépenses
final expenseCategories = await ref.read(categoriesByTypeProvider(CategoryType.expense).future);

// Récupérer les sous-catégories d'une catégorie
final subCategories = await ref.read(subCategoriesProvider(categoryId).future);

// Créer une nouvelle catégorie
await categoryService.createCategory(
  name: 'Ma catégorie',
  type: CategoryType.expense,
  iconName: 'shopping_cart',
  colorValue: Colors.blue.value,
  userId: currentUserId,
);
```

### Pour les utilisateurs

1. Accéder à la gestion des catégories via l'onglet "Catégories"
2. Basculer entre dépenses et revenus
3. Ajouter de nouvelles catégories avec le bouton "+"
4. Étendre une catégorie pour voir ses sous-catégories
5. Utiliser les actions (éditer, supprimer) sur chaque élément

## Fonctionnalités futures

- Synchronisation avec Supabase
- Import/export de catégories
- Statistiques par catégorie
- Catégories favorites
- Recherche et filtrage avancés