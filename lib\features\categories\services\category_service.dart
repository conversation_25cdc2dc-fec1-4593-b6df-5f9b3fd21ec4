import 'package:uuid/uuid.dart';
import '../../../core/services/storage_service.dart';
import '../domain/category.dart';
import '../domain/subcategory.dart';

class CategoryService {
  final StorageService _storage;
  final Uuid _uuid = const Uuid();

  CategoryService(this._storage);

  // Alias pour la compatibilité avec main.dart
  Future<void> initializePredefinedCategories() async {
    return initializeDefaultCategories();
  }

  // Initialiser les catégories et sous-catégories prédéfinies
  Future<void> initializeDefaultCategories() async {
    final existingCategories = await getAllCategories();
    if (existingCategories.isNotEmpty) return; // Déjà initialisées

    final now = DateTime.now();

    // Catégories prédéfinies
    final defaultCategories = [
      // Catégories de dépenses
      Category(
        id: 'cat_logement',
        name: 'Logement',
        type: CategoryType.expense,
        iconName: 'home',
        color: '#FF6B6B',
        createdAt: now,
        updatedAt: now,
      ),
      Category(
        id: 'cat_alimentation',
        name: 'Alimentation',
        type: CategoryType.expense,
        iconName: 'restaurant',
        color: '#4ECDC4',
        createdAt: now,
        updatedAt: now,
      ),
      Category(
        id: 'cat_transport',
        name: 'Transport',
        type: CategoryType.expense,
        iconName: 'directions_car',
        color: '#45B7D1',
        createdAt: now,
        updatedAt: now,
      ),
      Category(
        id: 'cat_education',
        name: 'Éducation',
        type: CategoryType.expense,
        iconName: 'school',
        color: '#96CEB4',
        createdAt: now,
        updatedAt: now,
      ),
      Category(
        id: 'cat_sante',
        name: 'Santé',
        type: CategoryType.expense,
        iconName: 'local_hospital',
        color: '#FFEAA7',
        createdAt: now,
        updatedAt: now,
      ),
      Category(
        id: 'cat_vetements',
        name: 'Vêtements / Entretien',
        type: CategoryType.expense,
        iconName: 'checkroom',
        color: '#DDA0DD',
        createdAt: now,
        updatedAt: now,
      ),
      Category(
        id: 'cat_communication',
        name: 'Communication',
        type: CategoryType.expense,
        iconName: 'phone',
        color: '#98D8C8',
        createdAt: now,
        updatedAt: now,
      ),
      Category(
        id: 'cat_loisirs',
        name: 'Loisirs & Enfants',
        type: CategoryType.expense,
        iconName: 'sports_esports',
        color: '#F7DC6F',
        createdAt: now,
        updatedAt: now,
      ),
      Category(
        id: 'cat_evenements',
        name: 'Événements / Famille',
        type: CategoryType.expense,
        iconName: 'celebration',
        color: '#BB8FCE',
        createdAt: now,
        updatedAt: now,
      ),
      Category(
        id: 'cat_religion',
        name: 'Religion / Dîmes / Dons',
        type: CategoryType.expense,
        iconName: 'volunteer_activism',
        color: '#85C1E9',
        createdAt: now,
        updatedAt: now,
      ),
      Category(
        id: 'cat_epargne',
        name: 'Épargne / Investissement',
        type: CategoryType.expense,
        iconName: 'savings',
        color: '#82E0AA',
        createdAt: now,
        updatedAt: now,
      ),

      // Catégories de revenus
      Category(
        id: 'cat_salaire',
        name: 'Salaire',
        type: CategoryType.income,
        iconName: 'work',
        color: '#58D68D',
        createdAt: now,
        updatedAt: now,
      ),
      Category(
        id: 'cat_ventes',
        name: 'Ventes',
        type: CategoryType.income,
        iconName: 'store',
        color: '#5DADE2',
        createdAt: now,
        updatedAt: now,
      ),
      Category(
        id: 'cat_dons_recus',
        name: 'Dons reçus',
        type: CategoryType.income,
        iconName: 'card_giftcard',
        color: '#F8C471',
        createdAt: now,
        updatedAt: now,
      ),
    ];

    // Sauvegarder les catégories
    for (final category in defaultCategories) {
      await _storage.saveCategory(category);
    }

    // Sous-catégories prédéfinies
    final defaultSubCategories = [
      // Logement
      SubCategory(
        id: 'sub_loyer',
        name: 'Loyer / Hypothèque',
        categoryId: 'cat_logement',
        iconName: 'home_work',
        createdAt: now,
        updatedAt: now,
      ),
      SubCategory(
        id: 'sub_electricite',
        name: 'Électricité',
        categoryId: 'cat_logement',
        iconName: 'electrical_services',
        createdAt: now,
        updatedAt: now,
      ),
      SubCategory(
        id: 'sub_eau',
        name: 'Eau',
        categoryId: 'cat_logement',
        iconName: 'water_drop',
        createdAt: now,
        updatedAt: now,
      ),
      SubCategory(
        id: 'sub_entretien_maison',
        name: 'Entretien maison',
        categoryId: 'cat_logement',
        iconName: 'build',
        createdAt: now,
        updatedAt: now,
      ),

      // Alimentation
      SubCategory(
        id: 'sub_marche',
        name: 'Marché hebdomadaire',
        categoryId: 'cat_alimentation',
        iconName: 'local_grocery_store',
        createdAt: now,
        updatedAt: now,
      ),
      SubCategory(
        id: 'sub_supermarche',
        name: 'Supermarché',
        categoryId: 'cat_alimentation',
        iconName: 'shopping_cart',
        createdAt: now,
        updatedAt: now,
      ),
      SubCategory(
        id: 'sub_restaurant',
        name: 'Restaurant / Fast-food',
        categoryId: 'cat_alimentation',
        iconName: 'restaurant',
        createdAt: now,
        updatedAt: now,
      ),
      SubCategory(
        id: 'sub_boissons',
        name: 'Boissons',
        categoryId: 'cat_alimentation',
        iconName: 'local_bar',
        createdAt: now,
        updatedAt: now,
      ),

      // Transport
      SubCategory(
        id: 'sub_carburant',
        name: 'Carburant',
        categoryId: 'cat_transport',
        iconName: 'local_gas_station',
        createdAt: now,
        updatedAt: now,
      ),
      SubCategory(
        id: 'sub_transport_public',
        name: 'Transport public',
        categoryId: 'cat_transport',
        iconName: 'directions_bus',
        createdAt: now,
        updatedAt: now,
      ),
      SubCategory(
        id: 'sub_taxi',
        name: 'Taxi / Uber',
        categoryId: 'cat_transport',
        iconName: 'local_taxi',
        createdAt: now,
        updatedAt: now,
      ),
      SubCategory(
        id: 'sub_entretien_vehicule',
        name: 'Entretien véhicule',
        categoryId: 'cat_transport',
        iconName: 'car_repair',
        createdAt: now,
        updatedAt: now,
      ),

      // Éducation
      SubCategory(
        id: 'sub_frais_scolarite',
        name: 'Frais de scolarité',
        categoryId: 'cat_education',
        iconName: 'school',
        createdAt: now,
        updatedAt: now,
      ),
      SubCategory(
        id: 'sub_fournitures',
        name: 'Fournitures scolaires',
        categoryId: 'cat_education',
        iconName: 'edit',
        createdAt: now,
        updatedAt: now,
      ),
      SubCategory(
        id: 'sub_livres',
        name: 'Livres / Manuels',
        categoryId: 'cat_education',
        iconName: 'menu_book',
        createdAt: now,
        updatedAt: now,
      ),
      SubCategory(
        id: 'sub_cours_particuliers',
        name: 'Cours particuliers',
        categoryId: 'cat_education',
        iconName: 'person',
        createdAt: now,
        updatedAt: now,
      ),

      // Santé
      SubCategory(
        id: 'sub_medicaments',
        name: 'Médicaments',
        categoryId: 'cat_sante',
        iconName: 'medication',
        createdAt: now,
        updatedAt: now,
      ),
      SubCategory(
        id: 'sub_consultation',
        name: 'Consultation médicale',
        categoryId: 'cat_sante',
        iconName: 'medical_services',
        createdAt: now,
        updatedAt: now,
      ),
      SubCategory(
        id: 'sub_dentiste',
        name: 'Dentiste',
        categoryId: 'cat_sante',
        iconName: 'dentistry',
        createdAt: now,
        updatedAt: now,
      ),
      SubCategory(
        id: 'sub_assurance_sante',
        name: 'Assurance santé',
        categoryId: 'cat_sante',
        iconName: 'health_and_safety',
        createdAt: now,
        updatedAt: now,
      ),

      // Communication
      SubCategory(
        id: 'sub_telephone',
        name: 'Téléphone mobile',
        categoryId: 'cat_communication',
        iconName: 'phone_android',
        createdAt: now,
        updatedAt: now,
      ),
      SubCategory(
        id: 'sub_internet',
        name: 'Internet',
        categoryId: 'cat_communication',
        iconName: 'wifi',
        createdAt: now,
        updatedAt: now,
      ),
      SubCategory(
        id: 'sub_courrier',
        name: 'Courrier / Colis',
        categoryId: 'cat_communication',
        iconName: 'mail',
        createdAt: now,
        updatedAt: now,
      ),
    ];

    // Sauvegarder les sous-catégories
    for (final subCategory in defaultSubCategories) {
      await _storage.saveSubCategory(subCategory);
    }
  }

  // Obtenir toutes les catégories
  Future<List<Category>> getAllCategories() async {
    return _storage.getAllCategories();
  }

  // Obtenir les catégories par type
  Future<List<Category>> getCategoriesByType(CategoryType type) async {
    final categories = await getAllCategories();
    return categories.where((cat) => cat.type == type && cat.isActive).toList();
  }

  // Obtenir les sous-catégories d'une catégorie
  Future<List<SubCategory>> getSubCategoriesByCategory(
    String categoryId,
  ) async {
    return _storage.getSubCategoriesByCategory(categoryId);
  }

  // Créer une nouvelle catégorie personnalisée
  Future<Category> createCategory({
    required String name,
    required String userId,
    required CategoryType type,
    String? iconName,
    String? color,
  }) async {
    final category = Category(
      id: _uuid.v4(),
      name: name,
      userId: userId,
      type: type,
      iconName: iconName,
      color: color,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );

    await _storage.saveCategory(category);
    return category;
  }

  // Créer une nouvelle sous-catégorie personnalisée
  Future<SubCategory> createSubCategory({
    required String name,
    required String categoryId,
    required String userId,
    String? iconName,
  }) async {
    final subCategory = SubCategory(
      id: _uuid.v4(),
      name: name,
      categoryId: categoryId,
      userId: userId,
      iconName: iconName,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );

    await _storage.saveSubCategory(subCategory);
    return subCategory;
  }

  // Modifier une catégorie
  Future<Category> updateCategory(Category category) async {
    final updatedCategory = category.copyWith(updatedAt: DateTime.now());
    await _storage.saveCategory(updatedCategory);
    return updatedCategory;
  }

  // Modifier une sous-catégorie
  Future<SubCategory> updateSubCategory(SubCategory subCategory) async {
    final updatedSubCategory = subCategory.copyWith(updatedAt: DateTime.now());
    await _storage.saveSubCategory(updatedSubCategory);
    return updatedSubCategory;
  }

  // Supprimer une catégorie (désactiver)
  Future<void> deleteCategory(String categoryId) async {
    final categories = await getAllCategories();
    final category = categories.firstWhere((cat) => cat.id == categoryId);
    final updatedCategory = category.copyWith(
      isActive: false,
      updatedAt: DateTime.now(),
    );
    await _storage.saveCategory(updatedCategory);
  }

  // Supprimer une sous-catégorie (désactiver)
  Future<void> deleteSubCategory(String subCategoryId) async {
    final subCategories = _storage.getAllSubCategories();
    final subCategory = subCategories.firstWhere(
      (sub) => sub.id == subCategoryId,
    );
    final updatedSubCategory = subCategory.copyWith(
      isActive: false,
      updatedAt: DateTime.now(),
    );
    await _storage.saveSubCategory(updatedSubCategory);
  }

  // Obtenir une catégorie par ID
  Future<Category?> getCategoryById(String categoryId) async {
    final categories = await getAllCategories();
    try {
      return categories.firstWhere((cat) => cat.id == categoryId);
    } catch (e) {
      return null;
    }
  }

  // Obtenir une sous-catégorie par ID
  Future<SubCategory?> getSubCategoryById(String subCategoryId) async {
    final subCategories = _storage.getAllSubCategories();
    try {
      return subCategories.firstWhere((sub) => sub.id == subCategoryId);
    } catch (e) {
      return null;
    }
  }
}
