import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../domain/fixed_charge.dart';
import '../../providers/fixed_charge_providers.dart';
import '../widgets/fixed_charge_card.dart';
import '../widgets/fixed_charges_stats_card.dart';
import '../widgets/add_fixed_charge_dialog.dart';

class FixedChargesScreen extends ConsumerStatefulWidget {
  const FixedChargesScreen({super.key});

  @override
  ConsumerState<FixedChargesScreen> createState() => _FixedChargesScreenState();
}

class _FixedChargesScreenState extends ConsumerState<FixedChargesScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      appBar: AppBar(
        title: const Text('📌 Charges Fixes'),
        backgroundColor: theme.colorScheme.surface,
        foregroundColor: theme.colorScheme.onSurface,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () => refreshAllFixedChargeProviders(ref),
            tooltip: 'Actualiser',
          ),
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: () => _showAddChargeDialog(context),
            tooltip: 'Ajouter une charge fixe',
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(icon: Icon(Icons.list), text: 'Toutes'),
            Tab(icon: Icon(Icons.pending_actions), text: 'Actives'),
            Tab(icon: Icon(Icons.warning), text: 'En retard'),
            Tab(icon: Icon(Icons.schedule), text: 'À venir'),
          ],
          labelColor: theme.colorScheme.primary,
          unselectedLabelColor: theme.colorScheme.onSurface.withValues(
            alpha: 0.6,
          ),
          indicatorColor: theme.colorScheme.primary,
        ),
      ),
      body: Column(
        children: [
          // Statistiques
          const Padding(
            padding: EdgeInsets.all(16.0),
            child: FixedChargesStatsCard(),
          ),

          // Contenu des onglets
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                _buildAllChargesTab(),
                _buildActiveChargesTab(),
                _buildOverdueChargesTab(),
                _buildUpcomingChargesTab(),
              ],
            ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () => _showAddChargeDialog(context),
        backgroundColor: theme.colorScheme.primary,
        foregroundColor: theme.colorScheme.onPrimary,
        child: const Icon(Icons.add),
      ),
    );
  }

  Widget _buildAllChargesTab() {
    return Consumer(
      builder: (context, ref, child) {
        final chargesAsync = ref.watch(userFixedChargesProvider);

        return chargesAsync.when(
          data:
              (charges) =>
                  _buildChargesList(charges, 'Aucune charge fixe enregistrée'),
          loading: () => const Center(child: CircularProgressIndicator()),
          error:
              (error, stack) => Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const Icon(Icons.error, size: 64, color: Colors.red),
                    const SizedBox(height: 16),
                    Text('Erreur: $error'),
                    const SizedBox(height: 16),
                    ElevatedButton(
                      onPressed: () => ref.refresh(userFixedChargesProvider),
                      child: const Text('Réessayer'),
                    ),
                  ],
                ),
              ),
        );
      },
    );
  }

  Widget _buildActiveChargesTab() {
    return Consumer(
      builder: (context, ref, child) {
        final chargesAsync = ref.watch(activeFixedChargesProvider);

        return chargesAsync.when(
          data: (charges) => _buildChargesList(charges, 'Aucune charge active'),
          loading: () => const Center(child: CircularProgressIndicator()),
          error: (error, stack) => Center(child: Text('Erreur: $error')),
        );
      },
    );
  }

  Widget _buildOverdueChargesTab() {
    return Consumer(
      builder: (context, ref, child) {
        final chargesAsync = ref.watch(overdueFixedChargesProvider);

        return chargesAsync.when(
          data:
              (charges) =>
                  _buildChargesList(charges, 'Aucune charge en retard'),
          loading: () => const Center(child: CircularProgressIndicator()),
          error: (error, stack) => Center(child: Text('Erreur: $error')),
        );
      },
    );
  }

  Widget _buildUpcomingChargesTab() {
    return Consumer(
      builder: (context, ref, child) {
        final chargesAsync = ref.watch(upcomingChargesWeekProvider);

        return chargesAsync.when(
          data:
              (charges) => _buildChargesList(
                charges,
                'Aucune charge à venir cette semaine',
              ),
          loading: () => const Center(child: CircularProgressIndicator()),
          error: (error, stack) => Center(child: Text('Erreur: $error')),
        );
      },
    );
  }

  Widget _buildChargesList(List<FixedCharge> charges, String emptyMessage) {
    if (charges.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.event_note, size: 64, color: Colors.grey[400]),
            const SizedBox(height: 16),
            Text(
              emptyMessage,
              style: TextStyle(fontSize: 16, color: Colors.grey[600]),
            ),
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: () => _showAddChargeDialog(context),
              icon: const Icon(Icons.add),
              label: const Text('Ajouter une charge fixe'),
            ),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: () async {
        refreshAllFixedChargeProviders(ref);
      },
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: charges.length,
        itemBuilder: (context, index) {
          final charge = charges[index];
          return Padding(
            padding: const EdgeInsets.only(bottom: 12),
            child: FixedChargeCard(
              charge: charge,
              onTap: () => _navigateToChargeDetails(charge),
              onMarkAsPaid: () => _markChargeAsPaid(charge),
              onToggleStatus: () => _toggleChargeStatus(charge),
            ),
          );
        },
      ),
    );
  }

  void _showAddChargeDialog(BuildContext context) {
    showDialog(
      context: context,
      builder:
          (context) => AddFixedChargeDialog(
            onChargeAdded: () {
              refreshAllFixedChargeProviders(ref);
            },
          ),
    );
  }

  void _navigateToChargeDetails(FixedCharge charge) {
    Navigator.of(
      context,
    ).pushNamed('/fixed-charge-details', arguments: charge.id);
  }

  Future<void> _markChargeAsPaid(FixedCharge charge) async {
    try {
      final chargeService = ref.read(fixedChargeServiceProvider);
      await chargeService.markChargeAsPaid(
        chargeId: charge.id,
        paymentDate: DateTime.now(),
      );

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('${charge.name} marquée comme payée'),
            backgroundColor: Colors.green,
          ),
        );
        refreshAllFixedChargeProviders(ref);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Erreur: $e'), backgroundColor: Colors.red),
        );
      }
    }
  }

  Future<void> _toggleChargeStatus(FixedCharge charge) async {
    try {
      final chargeService = ref.read(fixedChargeServiceProvider);
      await chargeService.toggleFixedChargeStatus(charge.id);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              charge.isActive
                  ? '${charge.name} désactivée'
                  : '${charge.name} activée',
            ),
            backgroundColor: Colors.blue,
          ),
        );
        refreshAllFixedChargeProviders(ref);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Erreur: $e'), backgroundColor: Colors.red),
        );
      }
    }
  }
}
