import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:gestion_budget_famille_zoro/app/app.dart';
import 'package:gestion_budget_famille_zoro/core/providers/app_providers.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize Hive
  await Hive.initFlutter();

  // Créer un ProviderContainer pour initialiser les services avant de lancer l'application
  final container = ProviderContainer();

  // Initialiser le StorageService via le provider
  final storageService = container.read(storageServiceProvider);
  await storageService.init();

  // Initialize Notification Service
  final notificationService = container.read(notificationServiceProvider);
  await notificationService.init();
  await notificationService.requestPermissions();

  // Create default admin user if no users exist
  final authService = container.read(authServiceProvider);
  final users = storageService.getAllUsers();
  if (users.isEmpty) {
    await authService.register(
      email: '<EMAIL>',
      password: 'admin123',
      firstName: 'Admin',
      lastName: 'Zoro',
    );
  }

  // Initialize CategoryService with predefined categories
  final categoryService = container.read(categoryServiceProvider);
  await categoryService.initializePredefinedCategories();

  // Initialize Supabase (optional - for future backend integration)
  // await Supabase.initialize(
  //   url: 'YOUR_SUPABASE_URL',
  //   anonKey: 'YOUR_SUPABASE_ANON_KEY',
  // );

  // Note: Nous utilisons directement le container créé précédemment
  // au lieu d'utiliser le paramètre 'parent' qui est déprécié
  runApp(
    UncontrolledProviderScope(
      container: container,
      child: const ZoroBudgetApp(),
    ),
  );
}
