import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../services/storage_service.dart';
import '../services/auth_service.dart';
import '../services/transaction_service.dart';
import '../services/budget_service.dart';
import '../services/notification_service.dart';
import '../theme/app_theme.dart';
import '../../features/categories/services/category_service.dart';

// Storage Service Provider
final storageServiceProvider = Provider<StorageService>((ref) {
  return StorageService();
});

// Auth Service Provider
final authServiceProvider = Provider<AuthService>((ref) {
  final storage = ref.watch(storageServiceProvider);
  return AuthService(storage);
});

// Transaction Service Provider
final transactionServiceProvider = Provider<TransactionService>((ref) {
  final storage = ref.watch(storageServiceProvider);
  return TransactionService(storage);
});

// Budget Service Provider
final budgetServiceProvider = Provider<BudgetService>((ref) {
  final storage = ref.watch(storageServiceProvider);
  return BudgetService(storage);
});

// Category Service Provider
final categoryServiceProvider = Provider<CategoryService>((ref) {
  final storage = ref.watch(storageServiceProvider);
  return CategoryService(storage);
});

// Notification Service Provider
final notificationServiceProvider = Provider<NotificationService>((ref) {
  return NotificationService();
});

// Theme Mode Provider
final themeModeProvider = StateNotifierProvider<ThemeModeNotifier, bool>((ref) {
  final storage = ref.watch(storageServiceProvider);
  return ThemeModeNotifier(storage);
});

// Theme Type Provider
final themeTypeProvider =
    StateNotifierProvider<ThemeTypeNotifier, AppThemeType>((ref) {
      final storage = ref.watch(storageServiceProvider);
      return ThemeTypeNotifier(storage);
    });

class ThemeModeNotifier extends StateNotifier<bool> {
  final StorageService _storage;

  ThemeModeNotifier(this._storage) : super(false) {
    // Nous ne chargeons pas le thème immédiatement pour éviter les erreurs d'initialisation
    // Le thème sera chargé une fois que l'application sera complètement initialisée
  }

  Future<void> loadThemeMode() async {
    try {
      final isDark = await _storage.getThemeMode();
      state = isDark;
    } catch (e) {
      // En cas d'erreur, on garde la valeur par défaut (false = thème clair)
      if (kDebugMode) {
        debugPrint('Erreur lors du chargement du thème: $e');
      }
    }
  }

  void toggleTheme() async {
    state = !state;
    try {
      await _storage.saveThemeMode(state);
    } catch (e) {
      debugPrint('Erreur lors de la sauvegarde du thème: $e');
    }
  }
}

class ThemeTypeNotifier extends StateNotifier<AppThemeType> {
  final StorageService _storage;

  ThemeTypeNotifier(this._storage) : super(AppThemeType.blue) {
    // Nous ne chargeons pas le type de thème immédiatement pour éviter les erreurs d'initialisation
  }

  Future<void> loadThemeType() async {
    try {
      final themeIndex = await _storage.getThemeType();
      state = AppThemeType.values[themeIndex];
    } catch (e) {
      // En cas d'erreur, on garde la valeur par défaut (AppThemeType.blue)
      if (kDebugMode) {
        debugPrint('Erreur lors du chargement du type de thème: $e');
      }
    }
  }

  void setThemeType(AppThemeType themeType) async {
    state = themeType;
    try {
      await _storage.saveThemeType(themeType.index);
    } catch (e) {
      debugPrint('Erreur lors de la sauvegarde du type de thème: $e');
    }
  }
}

// Language Provider
final languageProvider = StateNotifierProvider<LanguageNotifier, String>((ref) {
  final storage = ref.watch(storageServiceProvider);
  return LanguageNotifier(storage);
});

class LanguageNotifier extends StateNotifier<String> {
  final StorageService _storage;

  LanguageNotifier(this._storage) : super('fr') {
    // Nous ne chargeons pas la langue immédiatement pour éviter les erreurs d'initialisation
    // La langue sera chargée une fois que l'application sera complètement initialisée
  }

  Future<void> loadLanguage() async {
    try {
      final language = await _storage.getLanguage();
      state = language;
    } catch (e) {
      // En cas d'erreur, on garde la valeur par défaut ('fr')
      debugPrint('Erreur lors du chargement de la langue: $e');
    }
  }

  void setLanguage(String language) async {
    state = language;
    try {
      await _storage.saveLanguage(language);
    } catch (e) {
      debugPrint('Erreur lors de la sauvegarde de la langue: $e');
    }
  }
}

// Notifications Enabled Provider
final notificationsEnabledProvider =
    StateNotifierProvider<NotificationsNotifier, bool>((ref) {
      final storage = ref.watch(storageServiceProvider);
      return NotificationsNotifier(storage);
    });

class NotificationsNotifier extends StateNotifier<bool> {
  final StorageService _storage;

  NotificationsNotifier(this._storage) : super(true) {
    // Nous ne chargeons pas les notifications immédiatement pour éviter les erreurs d'initialisation
    // Les notifications seront chargées une fois que l'application sera complètement initialisée
  }

  Future<void> loadNotificationsEnabled() async {
    try {
      final enabled = await _storage.getNotificationsEnabled();
      state = enabled;
    } catch (e) {
      // En cas d'erreur, on garde la valeur par défaut (true)
      debugPrint('Erreur lors du chargement des notifications: $e');
    }
  }

  void toggleNotifications() async {
    state = !state;
    try {
      await _storage.saveNotificationsEnabled(state);
    } catch (e) {
      debugPrint('Erreur lors de la sauvegarde des notifications: $e');
    }
  }
}
