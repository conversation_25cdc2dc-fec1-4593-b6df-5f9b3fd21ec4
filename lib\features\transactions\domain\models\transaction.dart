class Transaction {
  final String id;
  final String userId;
  final String categoryId;
  final String accountId;
  final String name;
  final int amount;
  final bool isExpense;
  final String? description;
  final DateTime date;
  final DateTime createdAt;
  final DateTime updatedAt;

  Transaction({
    required this.id,
    required this.userId,
    required this.categoryId,
    required this.accountId,
    required this.name,
    required this.amount,
    required this.isExpense,
    this.description,
    required this.date,
    required this.createdAt,
    required this.updatedAt,
  });

  Transaction copyWith({
    String? id,
    String? userId,
    String? categoryId,
    String? accountId,
    String? name,
    int? amount,
    bool? isExpense,
    String? description,
    DateTime? date,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Transaction(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      categoryId: categoryId ?? this.categoryId,
      accountId: accountId ?? this.accountId,
      name: name ?? this.name,
      amount: amount ?? this.amount,
      isExpense: isExpense ?? this.isExpense,
      description: description ?? this.description,
      date: date ?? this.date,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}
