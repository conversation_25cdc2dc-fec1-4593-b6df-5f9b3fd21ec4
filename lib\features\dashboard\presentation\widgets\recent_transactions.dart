import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import '../../../../core/providers/app_providers.dart';
import '../../../../core/utils/currency_formatter.dart';
import '../../../../core/widgets/page_transitions.dart';
import '../../../transactions/domain/transaction.dart';
import '../../../transactions/presentation/screens/transactions_screen.dart';

class RecentTransactions extends ConsumerWidget {
  const RecentTransactions({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final transactionService = ref.watch(transactionServiceProvider);
    final authService = ref.watch(authServiceProvider);

    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  'Transactions récentes',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
                TextButton(
                  onPressed: () {
                    context.pushWithAnimation(
                      const TransactionsScreen(),
                      type: PageTransitionType.fade,
                    );
                  },
                  child: const Text('Voir tout'),
                ),
              ],
            ),
            const SizedBox(height: 16),
            FutureBuilder<List<Transaction>>(
              future: getRecentTransactionsAsync(
                transactionService,
                authService.currentUser?.id ?? '',
              ),
              builder: (context, snapshot) {
                if (snapshot.connectionState == ConnectionState.waiting) {
                  return const Center(child: CircularProgressIndicator());
                }

                final transactions = snapshot.data ?? [];

                if (transactions.isEmpty) {
                  return Center(
                    child: Column(
                      children: [
                        Icon(
                          Icons.receipt_long_outlined,
                          size: 64,
                          color: Colors.grey[400],
                        ),
                        const SizedBox(height: 16),
                        Text(
                          'Aucune transaction récente',
                          style: TextStyle(
                            color: Colors.grey[600],
                            fontSize: 16,
                          ),
                        ),
                      ],
                    ),
                  );
                }

                return Column(
                  children:
                      transactions.map((transaction) {
                        return _TransactionItem(transaction: transaction);
                      }).toList(),
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  Future<List<Transaction>> getRecentTransactionsAsync(
    transactionService,
    String userId,
  ) async {
    return Future.value(transactionService.getRecentTransactions(userId));
  }
}

class _TransactionItem extends StatelessWidget {
  final Transaction transaction;

  const _TransactionItem({required this.transaction});

  String _getCategoryIcon(String category) {
    switch (category.toLowerCase()) {
      case 'alimentation':
        return '🛒';
      case 'transport':
        return '🚗';
      case 'logement':
        return '🏠';
      case 'santé':
        return '🏥';
      case 'éducation':
        return '📚';
      case 'loisirs':
        return '🎮';
      case 'vêtements':
        return '👕';
      case 'salaire':
      case 'revenu':
        return '💰';
      default:
        return '💳';
    }
  }

  @override
  Widget build(BuildContext context) {
    final isExpense = transaction.isExpense;
    final icon = _getCategoryIcon(transaction.categoryId);

    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[200]!),
      ),
      child: Row(
        children: [
          CircleAvatar(
            backgroundColor:
                isExpense
                    ? Colors.red.withValues(alpha: 0.1)
                    : Colors.green.withValues(alpha: 0.1),
            child: Text(icon, style: const TextStyle(fontSize: 20)),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  transaction.name,
                  style: const TextStyle(
                    fontWeight: FontWeight.w600,
                    fontSize: 14,
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 2),
                Row(
                  children: [
                    Text(
                      transaction.categoryId,
                      style: TextStyle(color: Colors.grey[600], fontSize: 12),
                    ),
                    const SizedBox(width: 8),
                    Text(
                      '•',
                      style: TextStyle(color: Colors.grey[400], fontSize: 12),
                    ),
                    const SizedBox(width: 8),
                    Text(
                      DateFormat('dd/MM').format(transaction.date),
                      style: TextStyle(color: Colors.grey[600], fontSize: 12),
                    ),
                  ],
                ),
              ],
            ),
          ),
          Column(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Text(
                '${isExpense ? '-' : '+'} ${CurrencyFormatter.formatTransaction(transaction.amount.toDouble())}',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: isExpense ? Colors.red[600] : Colors.green[600],
                  fontSize: 14,
                ),
              ),
              // Description field not available in current Transaction model
              /*Text(
                    'No description available',
                  style: TextStyle(
                    color: Colors.grey[500],
                    fontSize: 10,
                  ),
                  overflow: TextOverflow.ellipsis,
                ),*/
            ],
          ),
        ],
      ),
    );
  }
}
