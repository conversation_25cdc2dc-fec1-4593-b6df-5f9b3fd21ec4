import 'package:hive/hive.dart';

part 'repayment.g.dart';

@HiveType(typeId: 11)
class Repayment {
  @HiveField(0)
  final String id;
  
  @HiveField(1)
  final String debtId;
  
  @HiveField(2)
  final int amount; // Montant en centimes
  
  @HiveField(3)
  final DateTime paymentDate;
  
  @HiveField(4)
  final PaymentMethod method;
  
  @HiveField(5)
  final String? notes;
  
  @HiveField(6)
  final String? receiptPath; // Chemin vers la photo du reçu
  
  @HiveField(7)
  final DateTime createdAt;
  
  @HiveField(8)
  final DateTime updatedAt;

  Repayment({
    required this.id,
    required this.debtId,
    required this.amount,
    required this.paymentDate,
    required this.method,
    this.notes,
    this.receiptPath,
    required this.createdAt,
    required this.updatedAt,
  });

  // Getters
  String get formattedAmount {
    final amountInFcfa = amount / 100;
    return '${amountInFcfa.toStringAsFixed(0)} FCFA';
  }

  String get formattedDate {
    return '${paymentDate.day.toString().padLeft(2, '0')}/'
           '${paymentDate.month.toString().padLeft(2, '0')}/'
           '${paymentDate.year}';
  }

  Repayment copyWith({
    String? id,
    String? debtId,
    int? amount,
    DateTime? paymentDate,
    PaymentMethod? method,
    String? notes,
    String? receiptPath,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Repayment(
      id: id ?? this.id,
      debtId: debtId ?? this.debtId,
      amount: amount ?? this.amount,
      paymentDate: paymentDate ?? this.paymentDate,
      method: method ?? this.method,
      notes: notes ?? this.notes,
      receiptPath: receiptPath ?? this.receiptPath,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'debtId': debtId,
      'amount': amount,
      'paymentDate': paymentDate.toIso8601String(),
      'method': method.name,
      'notes': notes,
      'receiptPath': receiptPath,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }

  factory Repayment.fromJson(Map<String, dynamic> json) {
    return Repayment(
      id: json['id'],
      debtId: json['debtId'],
      amount: json['amount'],
      paymentDate: DateTime.parse(json['paymentDate']),
      method: PaymentMethod.values.firstWhere((e) => e.name == json['method']),
      notes: json['notes'],
      receiptPath: json['receiptPath'],
      createdAt: DateTime.parse(json['createdAt']),
      updatedAt: DateTime.parse(json['updatedAt']),
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Repayment && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'Repayment(id: $id, amount: $formattedAmount, date: $formattedDate)';
  }
}

@HiveType(typeId: 12)
enum PaymentMethod {
  @HiveField(0)
  cash, // Espèces
  
  @HiveField(1)
  mobileMoney, // Mobile Money
  
  @HiveField(2)
  bankTransfer, // Virement bancaire
  
  @HiveField(3)
  check, // Chèque
  
  @HiveField(4)
  card, // Carte bancaire
  
  @HiveField(5)
  other, // Autre
}

extension PaymentMethodExtension on PaymentMethod {
  String get displayName {
    switch (this) {
      case PaymentMethod.cash:
        return 'Espèces';
      case PaymentMethod.mobileMoney:
        return 'Mobile Money';
      case PaymentMethod.bankTransfer:
        return 'Virement bancaire';
      case PaymentMethod.check:
        return 'Chèque';
      case PaymentMethod.card:
        return 'Carte bancaire';
      case PaymentMethod.other:
        return 'Autre';
    }
  }

  String get emoji {
    switch (this) {
      case PaymentMethod.cash:
        return '💵';
      case PaymentMethod.mobileMoney:
        return '📱';
      case PaymentMethod.bankTransfer:
        return '🏦';
      case PaymentMethod.check:
        return '📝';
      case PaymentMethod.card:
        return '💳';
      case PaymentMethod.other:
        return '💰';
    }
  }
}

// Classe pour les statistiques de dette
class DebtSummary {
  final int totalPaid;
  final int remainingAmount;
  final double progressPercentage;
  final List<Repayment> repayments;

  DebtSummary({
    required this.totalPaid,
    required this.remainingAmount,
    required this.progressPercentage,
    required this.repayments,
  });

  String get formattedTotalPaid {
    final amountInFcfa = totalPaid / 100;
    return '${amountInFcfa.toStringAsFixed(0)} FCFA';
  }

  String get formattedRemainingAmount {
    final amountInFcfa = remainingAmount / 100;
    return '${amountInFcfa.toStringAsFixed(0)} FCFA';
  }

  String get formattedProgressPercentage {
    return '${progressPercentage.toStringAsFixed(1)}%';
  }

  bool get isFullyPaid => remainingAmount <= 0;
}
