import 'package:flutter/material.dart';

class AddSubCategoryDialog extends StatefulWidget {
  final String categoryName;
  final String? initialName;
  final String? initialIconName;
  final bool isEditing;
  final Function(String name, String? iconName) onSave;

  const AddSubCategoryDialog({
    super.key,
    required this.categoryName,
    required this.onSave,
    this.initialName,
    this.initialIconName,
    this.isEditing = false,
  });

  @override
  State<AddSubCategoryDialog> createState() => _AddSubCategoryDialogState();
}

class _AddSubCategoryDialogState extends State<AddSubCategoryDialog> {
  late TextEditingController _nameController;
  String? _selectedIconName;
  bool _isLoading = false;

  // Icônes disponibles pour les sous-catégories
  final List<Map<String, dynamic>> _availableIcons = [
    {'name': 'category', 'icon': Icons.category},
    {'name': 'home_work', 'icon': Icons.home_work},
    {'name': 'electrical_services', 'icon': Icons.electrical_services},
    {'name': 'water_drop', 'icon': Icons.water_drop},
    {'name': 'build', 'icon': Icons.build},
    {'name': 'local_grocery_store', 'icon': Icons.local_grocery_store},
    {'name': 'shopping_cart', 'icon': Icons.shopping_cart},
    {'name': 'restaurant', 'icon': Icons.restaurant},
    {'name': 'local_bar', 'icon': Icons.local_bar},
    {'name': 'local_gas_station', 'icon': Icons.local_gas_station},
    {'name': 'directions_bus', 'icon': Icons.directions_bus},
    {'name': 'local_taxi', 'icon': Icons.local_taxi},
    {'name': 'car_repair', 'icon': Icons.car_repair},
    {'name': 'school', 'icon': Icons.school},
    {'name': 'edit', 'icon': Icons.edit},
    {'name': 'menu_book', 'icon': Icons.menu_book},
    {'name': 'person', 'icon': Icons.person},
    {'name': 'medication', 'icon': Icons.medication},
    {'name': 'medical_services', 'icon': Icons.medical_services},
    {'name': 'health_and_safety', 'icon': Icons.health_and_safety},
    {'name': 'phone_android', 'icon': Icons.phone_android},
    {'name': 'wifi', 'icon': Icons.wifi},
    {'name': 'mail', 'icon': Icons.mail},
    {'name': 'sports_esports', 'icon': Icons.sports_esports},
    {'name': 'celebration', 'icon': Icons.celebration},
    {'name': 'checkroom', 'icon': Icons.checkroom},
    {'name': 'local_laundry_service', 'icon': Icons.local_laundry_service},
    {'name': 'fitness_center', 'icon': Icons.fitness_center},
    {'name': 'movie', 'icon': Icons.movie},
    {'name': 'music_note', 'icon': Icons.music_note},
    {'name': 'book', 'icon': Icons.book},
    {'name': 'computer', 'icon': Icons.computer},
    {'name': 'phone', 'icon': Icons.phone},
    {'name': 'tv', 'icon': Icons.tv},
  ];

  @override
  void initState() {
    super.initState();
    _nameController = TextEditingController(text: widget.initialName ?? '');
    _selectedIconName = widget.initialIconName ?? 'category';
  }

  @override
  void dispose() {
    _nameController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return AlertDialog(
      title: Text(
        widget.isEditing 
            ? 'Modifier la sous-catégorie'
            : 'Ajouter une sous-catégorie',
      ),
      content: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Catégorie parente (si pas en mode édition)
            if (!widget.isEditing) ..[
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: theme.colorScheme.primary.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: theme.colorScheme.primary.withOpacity(0.3),
                  ),
                ),
                child: Row(
                  children: [
                    Icon(
                      Icons.folder,
                      color: theme.colorScheme.primary,
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        'Catégorie: ${widget.categoryName}',
                        style: theme.textTheme.bodyMedium?.copyWith(
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 16),
            ],
            
            // Nom de la sous-catégorie
            TextField(
              controller: _nameController,
              decoration: const InputDecoration(
                labelText: 'Nom de la sous-catégorie',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.subdirectory_arrow_right),
              ),
              textCapitalization: TextCapitalization.words,
            ),
            const SizedBox(height: 16),
            
            // Sélection d'icône
            Text(
              'Icône (optionnel)',
              style: theme.textTheme.titleSmall?.copyWith(
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 8),
            Container(
              height: 150,
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey.withOpacity(0.3)),
                borderRadius: BorderRadius.circular(8),
              ),
              child: GridView.builder(
                padding: const EdgeInsets.all(8),
                gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: 6,
                  crossAxisSpacing: 4,
                  mainAxisSpacing: 4,
                ),
                itemCount: _availableIcons.length,
                itemBuilder: (context, index) {
                  final iconData = _availableIcons[index];
                  final isSelected = _selectedIconName == iconData['name'];
                  
                  return GestureDetector(
                    onTap: () {
                      setState(() {
                        _selectedIconName = iconData['name'];
                      });
                    },
                    child: Container(
                      decoration: BoxDecoration(
                        color: isSelected 
                            ? theme.colorScheme.primary.withOpacity(0.2)
                            : Colors.transparent,
                        borderRadius: BorderRadius.circular(6),
                        border: Border.all(
                          color: isSelected 
                              ? theme.colorScheme.primary
                              : Colors.transparent,
                          width: 2,
                        ),
                      ),
                      child: Icon(
                        iconData['icon'],
                        size: 18,
                        color: isSelected 
                            ? theme.colorScheme.primary
                            : Colors.grey[600],
                      ),
                    ),
                  );
                },
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Conseil: Choisissez une icône qui représente bien votre sous-catégorie',
              style: theme.textTheme.bodySmall?.copyWith(
                color: Colors.grey[600],
                fontStyle: FontStyle.italic,
              ),
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: _isLoading ? null : () => Navigator.of(context).pop(),
          child: const Text('Annuler'),
        ),
        ElevatedButton(
          onPressed: _isLoading ? null : _handleSave,
          child: _isLoading
              ? const SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(strokeWidth: 2),
                )
              : Text(widget.isEditing ? 'Modifier' : 'Ajouter'),
        ),
      ],
    );
  }

  void _handleSave() async {
    final name = _nameController.text.trim();
    
    if (name.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Veuillez saisir un nom pour la sous-catégorie'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    setState(() => _isLoading = true);
    
    try {
      await widget.onSave(name, _selectedIconName);
      if (mounted) {
        Navigator.of(context).pop();
      }
    } catch (e) {
      if (mounted) {
        setState(() => _isLoading = false);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Erreur: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}