// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'charge_payment.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class ChargePaymentAdapter extends TypeAdapter<ChargePayment> {
  @override
  final int typeId = 17;

  @override
  ChargePayment read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return ChargePayment(
      id: fields[0] as String,
      fixedChargeId: fields[1] as String,
      amount: fields[2] as int,
      paymentDate: fields[3] as DateTime,
      dueDate: fields[4] as DateTime,
      status: fields[5] as PaymentStatus,
      notes: fields[6] as String?,
      transactionId: fields[7] as String?,
      createdAt: fields[8] as DateTime,
      updatedAt: fields[9] as DateTime,
    );
  }

  @override
  void write(BinaryWriter writer, ChargePayment obj) {
    writer
      ..writeByte(10)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.fixedChargeId)
      ..writeByte(2)
      ..write(obj.amount)
      ..writeByte(3)
      ..write(obj.paymentDate)
      ..writeByte(4)
      ..write(obj.dueDate)
      ..writeByte(5)
      ..write(obj.status)
      ..writeByte(6)
      ..write(obj.notes)
      ..writeByte(7)
      ..write(obj.transactionId)
      ..writeByte(8)
      ..write(obj.createdAt)
      ..writeByte(9)
      ..write(obj.updatedAt);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is ChargePaymentAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class PaymentStatusAdapter extends TypeAdapter<PaymentStatus> {
  @override
  final int typeId = 18;

  @override
  PaymentStatus read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return PaymentStatus.paid;
      case 1:
        return PaymentStatus.pending;
      case 2:
        return PaymentStatus.overdue;
      case 3:
        return PaymentStatus.cancelled;
      default:
        return PaymentStatus.paid;
    }
  }

  @override
  void write(BinaryWriter writer, PaymentStatus obj) {
    switch (obj) {
      case PaymentStatus.paid:
        writer.writeByte(0);
        break;
      case PaymentStatus.pending:
        writer.writeByte(1);
        break;
      case PaymentStatus.overdue:
        writer.writeByte(2);
        break;
      case PaymentStatus.cancelled:
        writer.writeByte(3);
        break;
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is PaymentStatusAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
