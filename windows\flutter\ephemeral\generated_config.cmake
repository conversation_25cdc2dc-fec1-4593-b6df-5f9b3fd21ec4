# Generated code do not commit.
file(TO_CMAKE_PATH "C:\\flutter\\src\\flutter" FLUTTER_ROOT)
file(TO_CMAKE_PATH "J:\\Gestion_Budget_famille_Zoro" PROJECT_DIR)

set(FLUTTER_VERSION "1.0.0+1" PARENT_SCOPE)
set(FLUTTER_VERSION_MAJOR 1 PARENT_SCOPE)
set(FLUTTER_VERSION_MINOR 0 PARENT_SCOPE)
set(FLUTTER_VERSION_PATCH 0 PARENT_SCOPE)
set(FLUTTER_VERSION_BUILD 1 PARENT_SCOPE)

# Environment variables to pass to tool_backend.sh
list(APPEND FLUTTER_TOOL_ENVIRONMENT
  "FLUTTER_ROOT=C:\\flutter\\src\\flutter"
  "PROJECT_DIR=J:\\Gestion_Budget_famille_Zoro"
  "FLUTTER_ROOT=C:\\flutter\\src\\flutter"
  "FLUTTER_EPHEMERAL_DIR=J:\\Gestion_Budget_famille_Zoro\\windows\\flutter\\ephemeral"
  "PROJECT_DIR=J:\\Gestion_Budget_famille_Zoro"
  "FLUTTER_TARGET=J:\\Gestion_Budget_famille_Zoro\\lib\\main.dart"
  "DART_OBFUSCATION=false"
  "TRACK_WIDGET_CREATION=true"
  "TREE_SHAKE_ICONS=false"
  "PACKAGE_CONFIG=J:\\Gestion_Budget_famille_Zoro\\.dart_tool\\package_config.json"
)
