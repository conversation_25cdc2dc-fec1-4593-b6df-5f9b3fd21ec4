# Family Budgets

## Description
Application de gestion budgétaire familiale développée en Flutter.

## Informations du projet
- **Version**: 1.0.0
- **Auteur**: Zoro Bi Menan V.
- **Plateforme**: Flutter
- **Langue**: Français

## Fonctionnalités principales
- Gestion des budgets familiaux
- Suivi des dépenses
- Catégorisation des transactions
- Interface utilisateur intuitive

## Installation
1. Clonez le repository
2. Exécutez `flutter pub get` pour installer les dépendances
3. Lancez l'application avec `flutter run`

## Structure du projet
- `lib/` - Code source principal de l'application
- `assets/` - Ressources (images, logos)
- `test/` - Tests unitaires

## Développement
Pour plus d'informations sur le développement Flutter :
- [Documentation officielle Flutter](https://docs.flutter.dev/)
- [Lab: Première application Flutter](https://docs.flutter.dev/get-started/codelab)
- [Cookbook: Exemples Flutter](https://docs.flutter.dev/cookbook)

## Licence
Projet privé - Tous droits réservés à Zoro Bi Menan V.
