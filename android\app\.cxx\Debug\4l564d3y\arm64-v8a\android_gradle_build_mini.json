{"buildFiles": ["C:\\flutter\\src\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "J:\\Gestion_Budget_famille_Zoro\\android\\app\\.cxx\\Debug\\4l564d3y\\arm64-v8a", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "J:\\Gestion_Budget_famille_Zoro\\android\\app\\.cxx\\Debug\\4l564d3y\\arm64-v8a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}