import 'package:flutter/material.dart';
import '../../domain/category.dart';

class AddCategoryDialog extends StatefulWidget {
  final CategoryType type;
  final String? initialName;
  final String? initialIconName;
  final String? initialColor;
  final bool isEditing;
  final Function(String name, String? iconName, String? color) onSave;

  const AddCategoryDialog({
    super.key,
    required this.type,
    required this.onSave,
    this.initialName,
    this.initialIconName,
    this.initialColor,
    this.isEditing = false,
  });

  @override
  State<AddCategoryDialog> createState() => _AddCategoryDialogState();
}

class _AddCategoryDialogState extends State<AddCategoryDialog> {
  late TextEditingController _nameController;
  String? _selectedIconName;
  String? _selectedColor;
  bool _isLoading = false;

  // Icônes disponibles
  final List<Map<String, dynamic>> _availableIcons = [
    {'name': 'category', 'icon': Icons.category},
    {'name': 'home', 'icon': Icons.home},
    {'name': 'restaurant', 'icon': Icons.restaurant},
    {'name': 'directions_car', 'icon': Icons.directions_car},
    {'name': 'school', 'icon': Icons.school},
    {'name': 'local_hospital', 'icon': Icons.local_hospital},
    {'name': 'checkroom', 'icon': Icons.checkroom},
    {'name': 'phone', 'icon': Icons.phone},
    {'name': 'sports_esports', 'icon': Icons.sports_esports},
    {'name': 'celebration', 'icon': Icons.celebration},
    {'name': 'volunteer_activism', 'icon': Icons.volunteer_activism},
    {'name': 'savings', 'icon': Icons.savings},
    {'name': 'work', 'icon': Icons.work},
    {'name': 'store', 'icon': Icons.store},
    {'name': 'card_giftcard', 'icon': Icons.card_giftcard},
    {'name': 'shopping_cart', 'icon': Icons.shopping_cart},
    {'name': 'local_gas_station', 'icon': Icons.local_gas_station},
    {'name': 'build', 'icon': Icons.build},
    {'name': 'medical_services', 'icon': Icons.medical_services},
    {'name': 'wifi', 'icon': Icons.wifi},
    {'name': 'mail', 'icon': Icons.mail},
  ];

  // Couleurs disponibles
  final List<String> _availableColors = [
    '#FF6B6B', // Rouge
    '#4ECDC4', // Turquoise
    '#45B7D1', // Bleu
    '#96CEB4', // Vert menthe
    '#FFEAA7', // Jaune
    '#DDA0DD', // Violet
    '#98D8C8', // Vert d'eau
    '#F7DC6F', // Jaune doré
    '#BB8FCE', // Lavande
    '#85C1E9', // Bleu clair
    '#82E0AA', // Vert clair
    '#58D68D', // Vert
    '#5DADE2', // Bleu moyen
    '#F8C471', // Orange
  ];

  @override
  void initState() {
    super.initState();
    _nameController = TextEditingController(text: widget.initialName ?? '');
    _selectedIconName = widget.initialIconName ?? 'category';
    _selectedColor = widget.initialColor ?? _availableColors[0];
  }

  @override
  void dispose() {
    _nameController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return AlertDialog(
      title: Text(
        widget.isEditing 
            ? 'Modifier la catégorie'
            : 'Ajouter une catégorie',
      ),
      content: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Type de catégorie (lecture seule)
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: widget.type == CategoryType.expense 
                    ? Colors.red.withOpacity(0.1)
                    : Colors.green.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: widget.type == CategoryType.expense 
                      ? Colors.red.withOpacity(0.3)
                      : Colors.green.withOpacity(0.3),
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    widget.type == CategoryType.expense 
                        ? Icons.trending_down
                        : Icons.trending_up,
                    color: widget.type == CategoryType.expense 
                        ? Colors.red
                        : Colors.green,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    widget.type == CategoryType.expense 
                        ? 'Catégorie de dépense'
                        : 'Catégorie de revenu',
                    style: theme.textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 16),
            
            // Nom de la catégorie
            TextField(
              controller: _nameController,
              decoration: const InputDecoration(
                labelText: 'Nom de la catégorie',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.label),
              ),
              textCapitalization: TextCapitalization.words,
            ),
            const SizedBox(height: 16),
            
            // Sélection d'icône
            Text(
              'Icône',
              style: theme.textTheme.titleSmall?.copyWith(
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 8),
            Container(
              height: 120,
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey.withOpacity(0.3)),
                borderRadius: BorderRadius.circular(8),
              ),
              child: GridView.builder(
                padding: const EdgeInsets.all(8),
                gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: 6,
                  crossAxisSpacing: 4,
                  mainAxisSpacing: 4,
                ),
                itemCount: _availableIcons.length,
                itemBuilder: (context, index) {
                  final iconData = _availableIcons[index];
                  final isSelected = _selectedIconName == iconData['name'];
                  
                  return GestureDetector(
                    onTap: () {
                      setState(() {
                        _selectedIconName = iconData['name'];
                      });
                    },
                    child: Container(
                      decoration: BoxDecoration(
                        color: isSelected 
                            ? theme.colorScheme.primary.withOpacity(0.2)
                            : Colors.transparent,
                        borderRadius: BorderRadius.circular(6),
                        border: Border.all(
                          color: isSelected 
                              ? theme.colorScheme.primary
                              : Colors.transparent,
                          width: 2,
                        ),
                      ),
                      child: Icon(
                        iconData['icon'],
                        size: 20,
                        color: isSelected 
                            ? theme.colorScheme.primary
                            : Colors.grey[600],
                      ),
                    ),
                  );
                },
              ),
            ),
            const SizedBox(height: 16),
            
            // Sélection de couleur
            Text(
              'Couleur',
              style: theme.textTheme.titleSmall?.copyWith(
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 8),
            Container(
              height: 60,
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey.withOpacity(0.3)),
                borderRadius: BorderRadius.circular(8),
              ),
              child: GridView.builder(
                padding: const EdgeInsets.all(8),
                scrollDirection: Axis.horizontal,
                gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: 2,
                  crossAxisSpacing: 4,
                  mainAxisSpacing: 4,
                ),
                itemCount: _availableColors.length,
                itemBuilder: (context, index) {
                  final colorHex = _availableColors[index];
                  final color = Color(int.parse(colorHex.replaceFirst('#', '0xFF')));
                  final isSelected = _selectedColor == colorHex;
                  
                  return GestureDetector(
                    onTap: () {
                      setState(() {
                        _selectedColor = colorHex;
                      });
                    },
                    child: Container(
                      decoration: BoxDecoration(
                        color: color,
                        borderRadius: BorderRadius.circular(6),
                        border: Border.all(
                          color: isSelected 
                              ? Colors.black
                              : Colors.transparent,
                          width: 2,
                        ),
                      ),
                      child: isSelected 
                          ? const Icon(
                              Icons.check,
                              color: Colors.white,
                              size: 16,
                            )
                          : null,
                    ),
                  );
                },
              ),
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: _isLoading ? null : () => Navigator.of(context).pop(),
          child: const Text('Annuler'),
        ),
        ElevatedButton(
          onPressed: _isLoading ? null : _handleSave,
          child: _isLoading
              ? const SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(strokeWidth: 2),
                )
              : Text(widget.isEditing ? 'Modifier' : 'Ajouter'),
        ),
      ],
    );
  }

  void _handleSave() async {
    final name = _nameController.text.trim();
    
    if (name.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Veuillez saisir un nom pour la catégorie'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    setState(() => _isLoading = true);
    
    try {
      await widget.onSave(name, _selectedIconName, _selectedColor);
      if (mounted) {
        Navigator.of(context).pop();
      }
    } catch (e) {
      if (mounted) {
        setState(() => _isLoading = false);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Erreur: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}