import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/providers/app_providers.dart';
import '../../services/category_service.dart';
import '../../domain/category.dart';
import '../../domain/subcategory.dart';
import '../widgets/category_card.dart';
import '../widgets/add_category_dialog.dart';
import '../widgets/add_subcategory_dialog.dart';

class CategoriesScreen extends ConsumerStatefulWidget {
  const CategoriesScreen({super.key});

  @override
  ConsumerState<CategoriesScreen> createState() => _CategoriesScreenState();
}

class _CategoriesScreenState extends ConsumerState<CategoriesScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  late CategoryService _categoryService;
  List<Category> _expenseCategories = [];
  List<Category> _incomeCategories = [];
  final Map<String, List<SubCategory>> _subCategories = {};
  bool _isLoading = true;
  String? _currentUserId;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    
    // Utiliser le CategoryService depuis le provider au lieu de créer une nouvelle instance
    _categoryService = ref.read(categoryServiceProvider);
    
    // Obtenir l'ID utilisateur depuis AuthService
    final authService = ref.read(authServiceProvider);
    _currentUserId = authService.currentUser?.id;
    
    _initializeData();
  }

  Future<void> _initializeData() async {
    setState(() => _isLoading = true);

    try {
      // Initialiser les catégories par défaut si nécessaire
      await _categoryService.initializeDefaultCategories();

      // Charger les données
      await _loadCategories();
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Erreur lors du chargement: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  Future<void> _loadCategories() async {
    final allCategories = await _categoryService.getAllCategories();

    _expenseCategories =
        allCategories
            .where((cat) => cat.type == CategoryType.expense && cat.isActive)
            .toList();

    _incomeCategories =
        allCategories
            .where((cat) => cat.type == CategoryType.income && cat.isActive)
            .toList();

    // Charger les sous-catégories pour chaque catégorie
    _subCategories.clear();
    for (final category in allCategories) {
      final subCats = await _categoryService.getSubCategoriesByCategory(
        category.id,
      );
      _subCategories[category.id] = subCats;
    }

    if (mounted) {
      setState(() {});
    }
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Gestion des catégories'),
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(icon: Icon(Icons.trending_down), text: 'Dépenses'),
            Tab(icon: Icon(Icons.trending_up), text: 'Revenus'),
          ],
        ),
      ),
      body:
          _isLoading
              ? const Center(child: CircularProgressIndicator())
              : TabBarView(
                controller: _tabController,
                children: [
                  _buildCategoryList(_expenseCategories, CategoryType.expense),
                  _buildCategoryList(_incomeCategories, CategoryType.income),
                ],
              ),
      floatingActionButton: FloatingActionButton(
        onPressed: () => _showAddCategoryDialog(),
        child: const Icon(Icons.add),
      ),
    );
  }

  Widget _buildCategoryList(List<Category> categories, CategoryType type) {
    if (categories.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              type == CategoryType.expense
                  ? Icons.trending_down
                  : Icons.trending_up,
              size: 64,
              color: Colors.grey,
            ),
            const SizedBox(height: 16),
            Text(
              type == CategoryType.expense
                  ? 'Aucune catégorie de dépense'
                  : 'Aucune catégorie de revenu',
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(color: Colors.grey),
            ),
            const SizedBox(height: 8),
            const Text(
              'Appuyez sur + pour ajouter une catégorie',
              style: TextStyle(color: Colors.grey),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: categories.length,
      itemBuilder: (context, index) {
        final category = categories[index];
        final subCategories = _subCategories[category.id] ?? [];

        return CategoryCard(
          category: category,
          subCategories: subCategories,
          onEditCategory: () => _editCategory(category),
          onDeleteCategory: () => _deleteCategory(category),
          onAddSubCategory: () => _addSubCategory(category),
          onEditSubCategory: (subCategory) => _editSubCategory(subCategory),
          onDeleteSubCategory: (subCategory) => _deleteSubCategory(subCategory),
        );
      },
    );
  }

  void _showAddCategoryDialog() {
    final currentType =
        _tabController.index == 0 ? CategoryType.expense : CategoryType.income;

    showDialog(
      context: context,
      builder:
          (context) => AddCategoryDialog(
            type: currentType,
            onSave: (name, iconName, color) async {
              final scaffoldMessenger = ScaffoldMessenger.of(context);
              try {
                await _categoryService.createCategory(
                  name: name,
                  userId: _currentUserId,
                  type: currentType,
                  iconName: iconName,
                  color: color,
                );
                await _loadCategories();
                if (mounted) {
                  scaffoldMessenger.showSnackBar(
                    const SnackBar(
                      content: Text('Catégorie ajoutée avec succès'),
                      backgroundColor: Colors.green,
                    ),
                  );
                }
              } catch (e) {
                // print('Erreur lors de la création de catégorie: $e');
                if (mounted) {
                  scaffoldMessenger.showSnackBar(
                    SnackBar(
                      content: Text('Erreur lors de la création: $e'),
                      backgroundColor: Colors.red,
                      duration: const Duration(seconds: 5),
                    ),
                  );
                }
              }
            },
          ),
    );
  }

  void _editCategory(Category category) {
    showDialog(
      context: context,
      builder:
          (context) => AddCategoryDialog(
            type: category.type,
            initialName: category.name,
            initialIconName: category.iconName,
            initialColor: category.color,
            isEditing: true,
            onSave: (name, iconName, color) async {
              final scaffoldMessenger = ScaffoldMessenger.of(context);
              try {
                final updatedCategory = category.copyWith(
                  name: name,
                  iconName: iconName,
                  color: color,
                );
                await _categoryService.updateCategory(updatedCategory);
                await _loadCategories();
                if (mounted) {
                  scaffoldMessenger.showSnackBar(
                    const SnackBar(
                      content: Text('Catégorie modifiée avec succès'),
                      backgroundColor: Colors.green,
                    ),
                  );
                }
              } catch (e) {
                if (mounted) {
                  scaffoldMessenger.showSnackBar(
                    SnackBar(
                      content: Text('Erreur: $e'),
                      backgroundColor: Colors.red,
                    ),
                  );
                }
              }
            },
          ),
    );
  }

  void _deleteCategory(Category category) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Supprimer la catégorie'),
            content: Text(
              'Êtes-vous sûr de vouloir supprimer la catégorie "${category.name}" ?\n\n'
              'Cette action supprimera également toutes les sous-catégories associées.',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('Annuler'),
              ),
              TextButton(
                onPressed: () async {
                  final navigator = Navigator.of(context);
                  final scaffoldMessenger = ScaffoldMessenger.of(context);
                  try {
                    await _categoryService.deleteCategory(category.id);
                    await _loadCategories();
                    if (mounted) {
                      navigator.pop();
                      scaffoldMessenger.showSnackBar(
                        const SnackBar(
                          content: Text('Catégorie supprimée avec succès'),
                          backgroundColor: Colors.green,
                        ),
                      );
                    }
                  } catch (e) {
                    if (mounted) {
                      navigator.pop();
                      scaffoldMessenger.showSnackBar(
                        SnackBar(
                          content: Text('Erreur: $e'),
                          backgroundColor: Colors.red,
                        ),
                      );
                    }
                  }
                },
                style: TextButton.styleFrom(foregroundColor: Colors.red),
                child: const Text('Supprimer'),
              ),
            ],
          ),
    );
  }

  void _addSubCategory(Category category) {
    showDialog(
      context: context,
      builder:
          (context) => AddSubCategoryDialog(
            categoryName: category.name,
            onSave: (name, iconName) async {
              try {
                await _categoryService.createSubCategory(
                  name: name,
                  categoryId: category.id,
                  userId: _currentUserId ?? 'default',
                  iconName: iconName,
                );
                await _loadCategories();
                if (mounted) {
                  // ignore: use_build_context_synchronously
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('Sous-catégorie ajoutée avec succès'),
                      backgroundColor: Colors.green,
                    ),
                  );
                }
              } catch (e) {
                if (mounted) {
                  // ignore: use_build_context_synchronously
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('Erreur: $e'),
                      backgroundColor: Colors.red,
                    ),
                  );
                }
              }
            },
          ),
    );
  }

  void _editSubCategory(SubCategory subCategory) {
    showDialog(
      context: context,
      builder:
          (context) => AddSubCategoryDialog(
            categoryName: '', // Sera ignoré en mode édition
            initialName: subCategory.name,
            initialIconName: subCategory.iconName,
            isEditing: true,
            onSave: (name, iconName) async {
              try {
                final updatedSubCategory = subCategory.copyWith(
                  name: name,
                  iconName: iconName,
                );
                await _categoryService.updateSubCategory(updatedSubCategory);
                await _loadCategories();
                if (mounted) {
                  // ignore: use_build_context_synchronously
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('Sous-catégorie modifiée avec succès'),
                      backgroundColor: Colors.green,
                    ),
                  );
                }
              } catch (e) {
                if (mounted) {
                  // ignore: use_build_context_synchronously
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('Erreur: $e'),
                      backgroundColor: Colors.red,
                    ),
                  );
                }
              }
            },
          ),
    );
  }

  void _deleteSubCategory(SubCategory subCategory) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Supprimer la sous-catégorie'),
            content: Text(
              'Êtes-vous sûr de vouloir supprimer la sous-catégorie "${subCategory.name}" ?',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('Annuler'),
              ),
              TextButton(
                onPressed: () async {
                  final navigator = Navigator.of(context);
                  final scaffoldMessenger = ScaffoldMessenger.of(context);
                  try {
                    await _categoryService.deleteSubCategory(subCategory.id);
                    await _loadCategories();
                    if (mounted) {
                      navigator.pop();
                      scaffoldMessenger.showSnackBar(
                        const SnackBar(
                          content: Text('Sous-catégorie supprimée avec succès'),
                          backgroundColor: Colors.green,
                        ),
                      );
                    }
                  } catch (e) {
                    if (mounted) {
                      navigator.pop();
                      scaffoldMessenger.showSnackBar(
                        SnackBar(
                          content: Text('Erreur: $e'),
                          backgroundColor: Colors.red,
                        ),
                      );
                    }
                  }
                },
                style: TextButton.styleFrom(foregroundColor: Colors.red),
                child: const Text('Supprimer'),
              ),
            ],
          ),
    );
  }
}
