import 'package:hive/hive.dart';

part 'charge_payment.g.dart';

@HiveType(typeId: 17)
class ChargePayment {
  @HiveField(0)
  final String id;
  
  @HiveField(1)
  final String fixedChargeId;
  
  @HiveField(2)
  final int amount; // Montant payé en centimes
  
  @HiveField(3)
  final DateTime paymentDate;
  
  @HiveField(4)
  final DateTime dueDate; // Date d'échéance pour ce paiement
  
  @HiveField(5)
  final PaymentStatus status;
  
  @HiveField(6)
  final String? notes;
  
  @HiveField(7)
  final String? transactionId; // Lien vers une transaction si créée
  
  @HiveField(8)
  final DateTime createdAt;
  
  @HiveField(9)
  final DateTime updatedAt;

  ChargePayment({
    required this.id,
    required this.fixedChargeId,
    required this.amount,
    required this.paymentDate,
    required this.dueDate,
    required this.status,
    this.notes,
    this.transactionId,
    required this.createdAt,
    required this.updatedAt,
  });

  // Getters
  String get formattedAmount {
    final amountInFcfa = amount / 100;
    return '${amountInFcfa.toStringAsFixed(0)} FCFA';
  }

  String get formattedPaymentDate {
    return '${paymentDate.day.toString().padLeft(2, '0')}/'
           '${paymentDate.month.toString().padLeft(2, '0')}/'
           '${paymentDate.year}';
  }

  String get formattedDueDate {
    return '${dueDate.day.toString().padLeft(2, '0')}/'
           '${dueDate.month.toString().padLeft(2, '0')}/'
           '${dueDate.year}';
  }

  bool get isLate {
    return paymentDate.isAfter(dueDate);
  }

  int get daysLate {
    if (!isLate) return 0;
    return paymentDate.difference(dueDate).inDays;
  }

  ChargePayment copyWith({
    String? id,
    String? fixedChargeId,
    int? amount,
    DateTime? paymentDate,
    DateTime? dueDate,
    PaymentStatus? status,
    String? notes,
    String? transactionId,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return ChargePayment(
      id: id ?? this.id,
      fixedChargeId: fixedChargeId ?? this.fixedChargeId,
      amount: amount ?? this.amount,
      paymentDate: paymentDate ?? this.paymentDate,
      dueDate: dueDate ?? this.dueDate,
      status: status ?? this.status,
      notes: notes ?? this.notes,
      transactionId: transactionId ?? this.transactionId,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'fixedChargeId': fixedChargeId,
      'amount': amount,
      'paymentDate': paymentDate.toIso8601String(),
      'dueDate': dueDate.toIso8601String(),
      'status': status.name,
      'notes': notes,
      'transactionId': transactionId,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }

  factory ChargePayment.fromJson(Map<String, dynamic> json) {
    return ChargePayment(
      id: json['id'],
      fixedChargeId: json['fixedChargeId'],
      amount: json['amount'],
      paymentDate: DateTime.parse(json['paymentDate']),
      dueDate: DateTime.parse(json['dueDate']),
      status: PaymentStatus.values.firstWhere((e) => e.name == json['status']),
      notes: json['notes'],
      transactionId: json['transactionId'],
      createdAt: DateTime.parse(json['createdAt']),
      updatedAt: DateTime.parse(json['updatedAt']),
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ChargePayment && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'ChargePayment(id: $id, amount: $formattedAmount, date: $formattedPaymentDate, status: ${status.displayName})';
  }
}

@HiveType(typeId: 18)
enum PaymentStatus {
  @HiveField(0)
  paid, // Payée
  
  @HiveField(1)
  pending, // En attente
  
  @HiveField(2)
  overdue, // En retard
  
  @HiveField(3)
  cancelled, // Annulée
}

extension PaymentStatusExtension on PaymentStatus {
  String get displayName {
    switch (this) {
      case PaymentStatus.paid:
        return 'Payée';
      case PaymentStatus.pending:
        return 'En attente';
      case PaymentStatus.overdue:
        return 'En retard';
      case PaymentStatus.cancelled:
        return 'Annulée';
    }
  }

  String get emoji {
    switch (this) {
      case PaymentStatus.paid:
        return '✅';
      case PaymentStatus.pending:
        return '⏳';
      case PaymentStatus.overdue:
        return '🔴';
      case PaymentStatus.cancelled:
        return '❌';
    }
  }
}

// Classe pour les statistiques de charges fixes
class FixedChargesSummary {
  final int totalActiveCharges;
  final int totalMonthlyAmount;
  final int totalPaidThisMonth;
  final int totalOverdueCharges;
  final int totalDueSoonCharges;
  final List<ChargePayment> recentPayments;

  FixedChargesSummary({
    required this.totalActiveCharges,
    required this.totalMonthlyAmount,
    required this.totalPaidThisMonth,
    required this.totalOverdueCharges,
    required this.totalDueSoonCharges,
    required this.recentPayments,
  });

  String get formattedTotalMonthlyAmount {
    final amountInFcfa = totalMonthlyAmount / 100;
    return '${amountInFcfa.toStringAsFixed(0)} FCFA';
  }

  String get formattedTotalPaidThisMonth {
    final amountInFcfa = totalPaidThisMonth / 100;
    return '${amountInFcfa.toStringAsFixed(0)} FCFA';
  }

  double get paymentProgressPercentage {
    if (totalMonthlyAmount == 0) return 0.0;
    return (totalPaidThisMonth / totalMonthlyAmount) * 100;
  }

  String get formattedPaymentProgressPercentage {
    return '${paymentProgressPercentage.toStringAsFixed(1)}%';
  }
}
