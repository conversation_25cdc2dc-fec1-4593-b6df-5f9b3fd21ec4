// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'fixed_charge.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class FixedChargeAdapter extends TypeAdapter<FixedCharge> {
  @override
  final int typeId = 13;

  @override
  FixedCharge read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return FixedCharge(
      id: fields[0] as String,
      userId: fields[1] as String,
      name: fields[2] as String,
      amount: fields[3] as int,
      dayOfMonth: fields[4] as int,
      recurrence: fields[5] as ChargeRecurrence,
      categoryId: fields[6] as String?,
      description: fields[7] as String?,
      isActive: fields[8] as bool,
      reminderEnabled: fields[9] as bool,
      reminderDaysBefore: fields[10] as int,
      type: fields[11] as ChargeType,
      createdAt: fields[12] as DateTime,
      updatedAt: fields[13] as DateTime,
      lastPaidDate: fields[14] as DateTime?,
    );
  }

  @override
  void write(BinaryWriter writer, FixedCharge obj) {
    writer
      ..writeByte(15)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.userId)
      ..writeByte(2)
      ..write(obj.name)
      ..writeByte(3)
      ..write(obj.amount)
      ..writeByte(4)
      ..write(obj.dayOfMonth)
      ..writeByte(5)
      ..write(obj.recurrence)
      ..writeByte(6)
      ..write(obj.categoryId)
      ..writeByte(7)
      ..write(obj.description)
      ..writeByte(8)
      ..write(obj.isActive)
      ..writeByte(9)
      ..write(obj.reminderEnabled)
      ..writeByte(10)
      ..write(obj.reminderDaysBefore)
      ..writeByte(11)
      ..write(obj.type)
      ..writeByte(12)
      ..write(obj.createdAt)
      ..writeByte(13)
      ..write(obj.updatedAt)
      ..writeByte(14)
      ..write(obj.lastPaidDate);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is FixedChargeAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class ChargeRecurrenceAdapter extends TypeAdapter<ChargeRecurrence> {
  @override
  final int typeId = 14;

  @override
  ChargeRecurrence read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return ChargeRecurrence.weekly;
      case 1:
        return ChargeRecurrence.monthly;
      case 2:
        return ChargeRecurrence.quarterly;
      case 3:
        return ChargeRecurrence.yearly;
      default:
        return ChargeRecurrence.weekly;
    }
  }

  @override
  void write(BinaryWriter writer, ChargeRecurrence obj) {
    switch (obj) {
      case ChargeRecurrence.weekly:
        writer.writeByte(0);
        break;
      case ChargeRecurrence.monthly:
        writer.writeByte(1);
        break;
      case ChargeRecurrence.quarterly:
        writer.writeByte(2);
        break;
      case ChargeRecurrence.yearly:
        writer.writeByte(3);
        break;
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is ChargeRecurrenceAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class ChargeTypeAdapter extends TypeAdapter<ChargeType> {
  @override
  final int typeId = 15;

  @override
  ChargeType read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return ChargeType.housing;
      case 1:
        return ChargeType.utilities;
      case 2:
        return ChargeType.insurance;
      case 3:
        return ChargeType.subscription;
      case 4:
        return ChargeType.loan;
      case 5:
        return ChargeType.other;
      default:
        return ChargeType.housing;
    }
  }

  @override
  void write(BinaryWriter writer, ChargeType obj) {
    switch (obj) {
      case ChargeType.housing:
        writer.writeByte(0);
        break;
      case ChargeType.utilities:
        writer.writeByte(1);
        break;
      case ChargeType.insurance:
        writer.writeByte(2);
        break;
      case ChargeType.subscription:
        writer.writeByte(3);
        break;
      case ChargeType.loan:
        writer.writeByte(4);
        break;
      case ChargeType.other:
        writer.writeByte(5);
        break;
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is ChargeTypeAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class ChargeStatusAdapter extends TypeAdapter<ChargeStatus> {
  @override
  final int typeId = 16;

  @override
  ChargeStatus read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return ChargeStatus.pending;
      case 1:
        return ChargeStatus.dueSoon;
      case 2:
        return ChargeStatus.overdue;
      case 3:
        return ChargeStatus.inactive;
      default:
        return ChargeStatus.pending;
    }
  }

  @override
  void write(BinaryWriter writer, ChargeStatus obj) {
    switch (obj) {
      case ChargeStatus.pending:
        writer.writeByte(0);
        break;
      case ChargeStatus.dueSoon:
        writer.writeByte(1);
        break;
      case ChargeStatus.overdue:
        writer.writeByte(2);
        break;
      case ChargeStatus.inactive:
        writer.writeByte(3);
        break;
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is ChargeStatusAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
