# Logo Setup Documentation

## Overview
The Family Budgets application uses PNG logo format for the splash screen.

## Logo Files
- `assets/images/logo.png` - Primary PNG logo

## Implementation Details

### Splash Screen Integration
The logo is integrated into the splash screen with a fallback system:
1. **Primary**: Loads PNG logo
2. **Fallback**: If PNG fails, shows default wallet icon

### Code Changes

#### Modified Files
- `lib/features/splash/presentation/screens/splash_screen.dart`
- `lib/features/auth/presentation/screens/splash_screen.dart`
- Updated the `_buildDefaultLogo()` methods to use PNG logo directly

#### Dependencies Removed
- Removed `flutter_svg` dependency as SVG support is no longer needed

#### Assets Configuration
- `assets/images/` configured in `pubspec.yaml` assets section

### Logo Hierarchy
```
PNG Logo (Primary)
    ↓ (if fails)
Default Icon (Fallback)
```

## Usage
The logo system works automatically. No manual intervention required.
The splash screen will automatically load the PNG logo or show the default icon if the PNG fails to load.

## Future Maintenance
- To update logo: Replace `assets/images/logo.png`
- PNG provides broad compatibility and good quality
- Always test the logo display after updates