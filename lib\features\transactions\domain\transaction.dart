import 'package:hive/hive.dart';

part 'transaction.g.dart';

enum TransactionType {
  expense,
  income,
}

@HiveType(typeId: 1)
class Transaction {
  @HiveField(0)
  final String id;
  
  @HiveField(1)
  final String userId;
  
  @HiveField(2)
  final String categoryId;
  
  @HiveField(3)
  final String accountId;
  
  @HiveField(4)
  final String name;
  
  @HiveField(5)
  final int amount; // Amount in cents
  
  @HiveField(6)
  final bool isExpense;
  
  @HiveField(7)
  final String? description;
  
  @HiveField(8)
  final DateTime date;
  
  @HiveField(9)
  final DateTime createdAt;
  
  @HiveField(10)
  final DateTime updatedAt;

  Transaction({
    required this.id,
    required this.userId,
    required this.categoryId,
    required this.accountId,
    required this.name,
    required this.amount,
    required this.isExpense,
    this.description,
    required this.date,
    required this.createdAt,
    required this.updatedAt,
  });
  
  String get formattedAmount {
    final amountInFcfa = amount / 100;
    return '${amountInFcfa.toStringAsFixed(0)} FCFA';
  }
  
  String get transactionType => isExpense ? 'Dépense' : 'Revenu';
  
  Transaction copyWith({
    String? id,
    String? userId,
    String? categoryId,
    String? accountId,
    String? name,
    int? amount,
    bool? isExpense,
    String? description,
    DateTime? date,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Transaction(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      categoryId: categoryId ?? this.categoryId,
      accountId: accountId ?? this.accountId,
      name: name ?? this.name,
      amount: amount ?? this.amount,
      isExpense: isExpense ?? this.isExpense,
      description: description ?? this.description,
      date: date ?? this.date,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
  
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'userId': userId,
      'categoryId': categoryId,
      'accountId': accountId,
      'name': name,
      'amount': amount,
      'isExpense': isExpense,
      'description': description,
      'date': date.toIso8601String(),
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }
  
  factory Transaction.fromJson(Map<String, dynamic> json) {
    return Transaction(
      id: json['id'],
      userId: json['userId'],
      categoryId: json['categoryId'],
      accountId: json['accountId'],
      name: json['name'],
      amount: json['amount'],
      isExpense: json['isExpense'],
      description: json['description'],
      date: DateTime.parse(json['date']),
      createdAt: DateTime.parse(json['createdAt']),
      updatedAt: DateTime.parse(json['updatedAt']),
    );
  }
  
  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Transaction && other.id == id;
  }
  
  @override
  int get hashCode => id.hashCode;
  
  @override
  String toString() {
    return 'Transaction(id: $id, name: $name, amount: $formattedAmount, type: $transactionType)';
  }
}