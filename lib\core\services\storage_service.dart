import 'package:hive_flutter/hive_flutter.dart';
import '../../features/auth/domain/user.dart';
import '../../features/transactions/domain/transaction.dart';
import '../../features/budgets/domain/budget.dart';
import '../../features/budgets/domain/goal.dart';
import '../../features/categories/domain/category.dart';
import '../../features/categories/domain/subcategory.dart';
import '../../features/debts/domain/debt.dart';
import '../../features/debts/domain/repayment.dart';

class StorageService {
  static const String _userBoxName = 'users';
  static const String _transactionBoxName = 'transactions';
  static const String _budgetBoxName = 'budgets';
  static const String _settingsBoxName = 'settings';
  static const String _goalBoxName = 'goals';
  static const String _categoryBoxName = 'categories';
  static const String _subCategoryBoxName = 'subcategories';
  static const String _debtBoxName = 'debts';
  static const String _repaymentBoxName = 'repayments';

  // Clés pour les paramètres
  static const String _themeModeKey = 'theme_mode';
  static const String _themeTypeKey = 'theme_type';
  static const String _languageKey = 'language';
  static const String _currentUserIdKey = 'current_user_id';

  static const String _notificationsEnabledKey = 'notificationsEnabled';
  static const String _biometricEnabledKey = 'biometricEnabled';
  static const String _accessCodeKey = 'access_code';
  static const String _splashImagePathKey = 'splash_image_path';
  static const String _splashEnabledKey = 'splash_enabled';
  late Box<User> _userBox;
  late Box<Transaction> _transactionBox;
  late Box<Budget> _budgetBox;
  late Box _settingsBox;
  late Box<Goal> _goalBox;
  late Box<Category> _categoryBox;
  late Box<SubCategory> _subCategoryBox;
  late Box<Debt> _debtBox;
  late Box<Repayment> _repaymentBox;

  Future<void> init() async {
    try {
      // Register adapters
      if (!Hive.isAdapterRegistered(0)) {
        Hive.registerAdapter(UserAdapter());
      }
      if (!Hive.isAdapterRegistered(1)) {
        Hive.registerAdapter(TransactionAdapter());
      }
      if (!Hive.isAdapterRegistered(2)) {
        Hive.registerAdapter(BudgetAdapter());
      }
      if (!Hive.isAdapterRegistered(3)) {
        Hive.registerAdapter(GoalAdapter());
      }
      if (!Hive.isAdapterRegistered(4)) {
        Hive.registerAdapter(CategoryAdapter());
      }
      if (!Hive.isAdapterRegistered(5)) {
        Hive.registerAdapter(SubCategoryAdapter());
      }
      if (!Hive.isAdapterRegistered(6)) {
        Hive.registerAdapter(CategoryTypeAdapter());
      }
      if (!Hive.isAdapterRegistered(7)) {
        Hive.registerAdapter(DebtAdapter());
      }
      if (!Hive.isAdapterRegistered(8)) {
        Hive.registerAdapter(DebtTypeAdapter());
      }
      if (!Hive.isAdapterRegistered(9)) {
        Hive.registerAdapter(RepaymentTypeAdapter());
      }
      if (!Hive.isAdapterRegistered(10)) {
        Hive.registerAdapter(DebtStatusAdapter());
      }
      if (!Hive.isAdapterRegistered(11)) {
        Hive.registerAdapter(RepaymentAdapter());
      }
      if (!Hive.isAdapterRegistered(12)) {
        Hive.registerAdapter(PaymentMethodAdapter());
      }

      // Open boxes
      _userBox = await Hive.openBox<User>(_userBoxName);
      _transactionBox = await Hive.openBox<Transaction>(_transactionBoxName);
      _budgetBox = await Hive.openBox<Budget>(_budgetBoxName);
      _settingsBox = await Hive.openBox(_settingsBoxName);
      _goalBox = await Hive.openBox<Goal>(_goalBoxName);
      _categoryBox = await Hive.openBox<Category>(_categoryBoxName);
      _subCategoryBox = await Hive.openBox<SubCategory>(_subCategoryBoxName);
      _debtBox = await Hive.openBox<Debt>(_debtBoxName);
      _repaymentBox = await Hive.openBox<Repayment>(_repaymentBoxName);
    } catch (e) {
      // En cas d'erreur (probablement due aux changements d'adaptateurs), nettoyer et réessayer
      // print('Erreur lors de l\'initialisation de Hive: $e');
      await _clearHiveData();
      await _initializeAfterClear();
    }
  }

  Future<void> _clearHiveData() async {
    try {
      await Hive.deleteBoxFromDisk(_categoryBoxName);
      await Hive.deleteBoxFromDisk(_subCategoryBoxName);
      // print('Données Hive nettoyées avec succès');
    } catch (e) {
      // print('Erreur lors du nettoyage: $e');
    }
  }

  Future<void> _initializeAfterClear() async {
    _userBox = await Hive.openBox<User>(_userBoxName);
    _transactionBox = await Hive.openBox<Transaction>(_transactionBoxName);
    _budgetBox = await Hive.openBox<Budget>(_budgetBoxName);
    _settingsBox = await Hive.openBox(_settingsBoxName);
    _goalBox = await Hive.openBox<Goal>(_goalBoxName);
    _categoryBox = await Hive.openBox<Category>(_categoryBoxName);
    _subCategoryBox = await Hive.openBox<SubCategory>(_subCategoryBoxName);
    _debtBox = await Hive.openBox<Debt>(_debtBoxName);
    _repaymentBox = await Hive.openBox<Repayment>(_repaymentBoxName);
  }

  // User methods
  Future<void> saveUser(User user) async {
    await _userBox.put(user.id, user);
  }

  User? getUser(String id) {
    return _userBox.get(id);
  }

  Future<void> deleteUser(String id) async {
    await _userBox.delete(id);
  }

  List<User> getAllUsers() {
    return _userBox.values.toList();
  }

  // Transaction methods
  Future<void> saveTransaction(Transaction transaction) async {
    await _transactionBox.put(transaction.id, transaction);
  }

  Transaction? getTransaction(String id) {
    return _transactionBox.get(id);
  }

  Future<void> deleteTransaction(String id) async {
    await _transactionBox.delete(id);
  }

  List<Transaction> getAllTransactions() {
    return _transactionBox.values.toList();
  }

  List<Transaction> getTransactionsByUserId(String userId) {
    return _transactionBox.values
        .where((transaction) => transaction.userId == userId)
        .toList();
  }

  // Budget methods
  Future<void> saveBudget(Budget budget) async {
    await _budgetBox.put(budget.id, budget);
  }

  Budget? getBudget(String id) {
    return _budgetBox.get(id);
  }

  Future<void> deleteBudget(String id) async {
    await _budgetBox.delete(id);
  }

  List<Budget> getAllBudgets() {
    return _budgetBox.values.toList();
  }

  List<Budget> getBudgetsByUserId(String userId) {
    return _budgetBox.values
        .where((budget) => budget.userId == userId)
        .toList();
  }

  // Settings methods
  Future<void> saveThemeMode(bool isDark) async {
    await _settingsBox.put(_themeModeKey, isDark);
  }

  Future<bool> getThemeMode() async {
    return _settingsBox.get(_themeModeKey, defaultValue: false);
  }

  Future<void> saveThemeType(int themeTypeIndex) async {
    await _settingsBox.put(_themeTypeKey, themeTypeIndex);
  }

  Future<int> getThemeType() async {
    return _settingsBox.get(_themeTypeKey, defaultValue: 0);
  }

  Future<void> saveLanguage(String language) async {
    await _settingsBox.put(_languageKey, language);
  }

  Future<String> getLanguage() async {
    return _settingsBox.get(_languageKey, defaultValue: 'fr');
  }

  Future<void> saveNotificationsEnabled(bool enabled) async {
    await _settingsBox.put(_notificationsEnabledKey, enabled);
  }

  Future<bool> getNotificationsEnabled() async {
    return _settingsBox.get(_notificationsEnabledKey, defaultValue: true);
  }

  Future<void> saveBiometricEnabled(bool enabled) async {
    await _settingsBox.put(_biometricEnabledKey, enabled);
  }

  Future<bool> getBiometricEnabled() async {
    return _settingsBox.get(_biometricEnabledKey, defaultValue: false);
  }

  Future<void> saveCurrentUserId(String? userId) async {
    if (userId != null) {
      await _settingsBox.put(_currentUserIdKey, userId);
    } else {
      await _settingsBox.delete(_currentUserIdKey);
    }
  }

  Future<String?> getCurrentUserId() async {
    return _settingsBox.get(_currentUserIdKey);
  }

  // Clear all data
  Future<void> clearAllData() async {
    await _userBox.clear();
    await _transactionBox.clear();
    await _budgetBox.clear();
    await _settingsBox.clear();
    await _goalBox.clear();
    await _categoryBox.clear();
    await _subCategoryBox.clear();
  }

  // Close boxes
  Future<void> close() async {
    await _userBox.close();
    await _transactionBox.close();
    await _budgetBox.close();
    await _settingsBox.close();
    await _goalBox.close();
    await _categoryBox.close();
    await _subCategoryBox.close();
  }

  // Goal methods
  Future<void> saveGoal(Goal goal) async {
    await _goalBox.put(goal.id, goal);
  }

  Goal? getGoal(String id) {
    return _goalBox.get(id);
  }

  Future<void> deleteGoal(String id) async {
    await _goalBox.delete(id);
  }

  List<Goal> getAllGoals() {
    return _goalBox.values.toList();
  }

  List<Goal> getGoalsByUserId(String userId) {
    return _goalBox.values.where((goal) => goal.userId == userId).toList();
  }

  // Category methods
  Future<void> saveCategory(Category category) async {
    await _categoryBox.put(category.id, category);
  }

  Category? getCategory(String id) {
    return _categoryBox.get(id);
  }

  Future<void> deleteCategory(String id) async {
    await _categoryBox.delete(id);
  }

  List<Category> getAllCategories() {
    return _categoryBox.values.toList();
  }

  List<Category> getCategoriesByUserId(String userId) {
    return _categoryBox.values
        .where(
          (category) => category.userId == userId || category.userId == null,
        )
        .toList();
  }

  // SubCategory methods
  Future<void> saveSubCategory(SubCategory subCategory) async {
    await _subCategoryBox.put(subCategory.id, subCategory);
  }

  SubCategory? getSubCategory(String id) {
    return _subCategoryBox.get(id);
  }

  Future<void> deleteSubCategory(String id) async {
    await _subCategoryBox.delete(id);
  }

  List<SubCategory> getAllSubCategories() {
    return _subCategoryBox.values.toList();
  }

  List<SubCategory> getSubCategoriesByCategory(String categoryId) {
    return _subCategoryBox.values
        .where(
          (subCategory) =>
              subCategory.categoryId == categoryId && subCategory.isActive,
        )
        .toList();
  }

  List<SubCategory> getSubCategoriesByUserId(String userId) {
    return _subCategoryBox.values
        .where(
          (subCategory) =>
              subCategory.userId == userId || subCategory.userId == null,
        )
        .toList();
  }

  // Access Code methods
  Future<void> saveAccessCode(String code) async {
    await _settingsBox.put(_accessCodeKey, code);
  }

  Future<String?> getAccessCode() async {
    return _settingsBox.get(_accessCodeKey);
  }

  // Splash Screen methods
  Future<void> saveSplashImagePath(String? path) async {
    await _settingsBox.put(_splashImagePathKey, path);
  }

  Future<String?> getSplashImagePath() async {
    return _settingsBox.get(_splashImagePathKey);
  }

  Future<void> saveSplashEnabled(bool enabled) async {
    await _settingsBox.put(_splashEnabledKey, enabled);
  }

  Future<bool> getSplashEnabled() async {
    return _settingsBox.get(_splashEnabledKey, defaultValue: true);
  }

  // ==================== DEBTS ====================

  Future<void> saveDebt(Debt debt) async {
    await _debtBox.put(debt.id, debt);
  }

  Debt? getDebt(String id) {
    return _debtBox.get(id);
  }

  List<Debt> getAllDebts() {
    return _debtBox.values.toList();
  }

  Future<void> deleteDebt(String id) async {
    await _debtBox.delete(id);
  }

  // ==================== REPAYMENTS ====================

  Future<void> saveRepayment(Repayment repayment) async {
    await _repaymentBox.put(repayment.id, repayment);
  }

  Repayment? getRepayment(String id) {
    return _repaymentBox.get(id);
  }

  List<Repayment> getAllRepayments() {
    return _repaymentBox.values.toList();
  }

  Future<void> deleteRepayment(String id) async {
    await _repaymentBox.delete(id);
  }
}
