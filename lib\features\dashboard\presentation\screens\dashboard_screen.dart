import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:form_validator/form_validator.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../core/providers/app_providers.dart';
import '../../../categories/domain/category.dart';
import '../widgets/balance_card.dart';
import '../widgets/recent_transactions.dart';
import '../widgets/upcoming_tasks.dart';
import '../widgets/expense_chart.dart';
import '../widgets/budget_overview.dart';
import '../widgets/goal_overview.dart';
import '../../../transactions/presentation/screens/transactions_screen.dart';
import '../../../budgets/presentation/screens/budgets_screen.dart';
import '../../../categories/presentation/screens/categories_screen.dart';
import '../../../debts/presentation/screens/debts_screen.dart';
import '../../../profile/presentation/screens/profile_screen.dart';

class DashboardScreen extends ConsumerStatefulWidget {
  const DashboardScreen({super.key});

  @override
  ConsumerState<DashboardScreen> createState() => _DashboardScreenState();
}

class _DashboardScreenState extends ConsumerState<DashboardScreen> {
  int _selectedIndex = 0;

  final List<Widget> _screens = [
    const _DashboardHomeScreen(),
    const TransactionsScreen(),
    const BudgetsScreen(),
    const CategoriesScreen(),
    const DebtsScreen(),
    const ProfileScreen(),
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: IndexedStack(index: _selectedIndex, children: _screens),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          _showAddTransactionModal(context);
        },
        backgroundColor: AppTheme.primaryColor,
        elevation: 8,
        child: const Icon(Icons.add, color: Colors.white),
      ),
      floatingActionButtonLocation: FloatingActionButtonLocation.centerFloat,
      bottomNavigationBar: Container(
        margin: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.blue,
          borderRadius: BorderRadius.circular(25),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              blurRadius: 20,
              offset: const Offset(0, 5),
            ),
          ],
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(25),
          child: BottomNavigationBar(
            currentIndex: _selectedIndex,
            onTap: (index) {
              setState(() {
                _selectedIndex = index;
              });
            },
            type: BottomNavigationBarType.fixed,
            backgroundColor: Colors.transparent,
            elevation: 0,
            selectedItemColor: Colors.black,
            unselectedItemColor: Colors.white,
            selectedFontSize: 12,
            unselectedFontSize: 10,
            items: const [
              BottomNavigationBarItem(
                icon: Icon(Icons.dashboard),
                label: 'Tableau',
              ),
              BottomNavigationBarItem(
                icon: Icon(Icons.receipt_long),
                label: 'Transactions',
              ),
              BottomNavigationBarItem(
                icon: Icon(Icons.account_balance_wallet),
                label: 'Budgets',
              ),
              BottomNavigationBarItem(
                icon: Icon(Icons.category),
                label: 'Catégories',
              ),
              BottomNavigationBarItem(
                icon: Icon(Icons.credit_card),
                label: 'Dettes',
              ),
              BottomNavigationBarItem(
                icon: Icon(Icons.person),
                label: 'Profil',
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _showAddTransactionModal(BuildContext context) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => AddTransactionModal(),
    );
  }
}

class _DashboardHomeScreen extends ConsumerWidget {
  const _DashboardHomeScreen();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final transactionService = ref.watch(transactionServiceProvider);
    final authService = ref.watch(authServiceProvider);

    return SafeArea(
      child: RefreshIndicator(
        onRefresh: () async {
          // Actualiser les données depuis les services
          await Future.delayed(const Duration(milliseconds: 500));
        },
        child: ListView(
          padding: const EdgeInsets.all(16),
          children: [
            // Carte de solde avec données réelles
            FutureBuilder<List<double>>(
              future: Future.wait([
                transactionService.getTotalBalance(
                  authService.currentUser?.id ?? '',
                ),
                Future<double>(() {
                  // Calculer le solde du mois précédent
                  final now = DateTime.now();
                  final currentMonth = DateTime(now.year, now.month, 1);
                  final previousMonth = DateTime(now.year, now.month - 1, 1);

                  final currentMonthBalance = transactionService
                      .calculateMonthlyBalance(
                        authService.currentUser?.id ?? '',
                        currentMonth,
                      );

                  final previousMonthBalance = transactionService
                      .calculateMonthlyBalance(
                        authService.currentUser?.id ?? '',
                        previousMonth,
                      );

                  // Éviter la division par zéro
                  if (previousMonthBalance == 0) return 0.0;

                  // Calculer la variation en pourcentage
                  final variation =
                      ((currentMonthBalance - previousMonthBalance) /
                          previousMonthBalance.abs()) *
                      100;
                  return variation;
                }),
              ]),
              builder: (context, snapshot) {
                final balance = snapshot.data?[0] ?? 0.0;
                final variation = snapshot.data?[1] ?? 0.0;
                return BalanceCard(balance: balance, variation: variation);
              },
            ),
            const SizedBox(height: 24),

            // Graphique des dépenses par catégorie
            const ExpenseChart(),
            const SizedBox(height: 24),

            // Aperçu des budgets
            const BudgetOverview(),
            const SizedBox(height: 24),

            // Aperçu des objectifs d'épargne
            const GoalOverview(),
            const SizedBox(height: 24),

            // Transactions récentes
            const RecentTransactions(),
            const SizedBox(height: 24),

            // Tâches à venir
            const UpcomingTasks(),
            const SizedBox(height: 24),
          ],
        ),
      ),
    );
  }
}

class AddTransactionModal extends ConsumerStatefulWidget {
  const AddTransactionModal({super.key});

  @override
  ConsumerState<AddTransactionModal> createState() =>
      _AddTransactionModalState();
}

class _AddTransactionModalState extends ConsumerState<AddTransactionModal> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _amountController = TextEditingController();
  final _descriptionController = TextEditingController();
  bool _isExpense = true;
  bool _isLoading = false;
  String _selectedCategory = 'Alimentation';

  List<String> _categories = [];
  bool _categoriesLoaded = false;

  @override
  void initState() {
    super.initState();
    _loadCategories();
  }

  Future<void> _loadCategories() async {
    try {
      final categoryService = ref.read(categoryServiceProvider);
      final categories = await categoryService.getCategoriesByType(
        _isExpense ? CategoryType.expense : CategoryType.income,
      );
      setState(() {
        _categories = categories.map((c) => c.name).toList();
        _categoriesLoaded = true;
        if (_categories.isNotEmpty &&
            !_categories.contains(_selectedCategory)) {
          _selectedCategory = _categories.first;
        }
      });
    } catch (e) {
      // En cas d'erreur, utiliser des catégories par défaut
      setState(() {
        _categories =
            _isExpense
                ? [
                  'Alimentation',
                  'Transport',
                  'Logement',
                  'Santé',
                  'Éducation',
                  'Loisirs',
                  'Vêtements',
                  'Autres',
                ]
                : ['Salaire', 'Freelance', 'Investissements', 'Autres'];
        _categoriesLoaded = true;
        if (_categories.isNotEmpty &&
            !_categories.contains(_selectedCategory)) {
          _selectedCategory = _categories.first;
        }
      });
    }
  }

  Future<void> _addTransaction() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final transactionService = ref.read(transactionServiceProvider);
      final authService = ref.read(authServiceProvider);
      final notificationService = ref.read(notificationServiceProvider);

      final result = await transactionService.createTransaction(
        userId: authService.currentUser?.id ?? '',
        categoryId: _selectedCategory,
        accountId: 'default',
        name: _nameController.text.trim(),
        amount: (double.parse(_amountController.text) * 100).toInt(),
        isExpense: _isExpense,
        description: _descriptionController.text.trim(),
        date: DateTime.now(),
      );

      if (!result.success) {
        throw Exception(result.message);
      }

      final transaction = result.transaction!;

      // Afficher une notification
      await notificationService.showTransactionConfirmation(transaction);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Transaction ajoutée avec succès'),
            backgroundColor: Colors.green,
          ),
        );
        Navigator.pop(context);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Erreur: $e'), backgroundColor: Colors.red),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.only(
        bottom: MediaQuery.of(context).viewInsets.bottom + 20,
        top: 20,
        left: 20,
        right: 20,
      ),
      child: Form(
        key: _formKey,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            const SizedBox(height: 20),
            const Text(
              'Ajouter une transaction',
              style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 20),

            // Type de transaction
            Row(
              children: [
                Expanded(
                  child: ChoiceChip(
                    label: const Text('Dépense'),
                    selected: _isExpense,
                    onSelected: (selected) {
                      setState(() {
                        _isExpense = true;
                      });
                      _loadCategories();
                    },
                    selectedColor: Colors.red[100],
                    labelStyle: TextStyle(
                      color: _isExpense ? Colors.red[700] : null,
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: ChoiceChip(
                    label: const Text('Revenu'),
                    selected: !_isExpense,
                    onSelected: (selected) {
                      setState(() {
                        _isExpense = false;
                      });
                      _loadCategories();
                    },
                    selectedColor: Colors.green[100],
                    labelStyle: TextStyle(
                      color: !_isExpense ? Colors.green[700] : null,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // Nom de la transaction
            TextFormField(
              controller: _nameController,
              decoration: const InputDecoration(
                labelText: 'Nom de la transaction',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.receipt),
              ),
              validator:
                  ValidationBuilder()
                      .required('Le nom est requis')
                      .minLength(
                        2,
                        'Le nom doit contenir au moins 2 caractères',
                      )
                      .build(),
            ),
            const SizedBox(height: 16),

            // Montant
            TextFormField(
              controller: _amountController,
              decoration: const InputDecoration(
                labelText: 'Montant (FCFA)',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.attach_money),
              ),
              keyboardType: TextInputType.number,
              validator:
                  ValidationBuilder()
                      .required('Le montant est requis')
                      .regExp(RegExp(r'^\d+(\.\d{1,2})?$'), 'Montant invalide')
                      .build(),
            ),
            const SizedBox(height: 16),

            // Catégorie
            _categoriesLoaded
                ? DropdownButtonFormField<String>(
                  value:
                      _categories.contains(_selectedCategory)
                          ? _selectedCategory
                          : null,
                  decoration: const InputDecoration(
                    labelText: 'Catégorie',
                    border: OutlineInputBorder(),
                    prefixIcon: Icon(Icons.category),
                  ),
                  items:
                      _categories.map((category) {
                        return DropdownMenuItem(
                          value: category,
                          child: Text(category),
                        );
                      }).toList(),
                  onChanged: (value) {
                    setState(() {
                      _selectedCategory = value!;
                    });
                  },
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Veuillez sélectionner une catégorie';
                    }
                    return null;
                  },
                )
                : Container(
                  height: 56,
                  decoration: BoxDecoration(
                    border: Border.all(color: Colors.grey),
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: const Center(
                    child: Row(
                      children: [
                        SizedBox(width: 12),
                        Icon(Icons.category, color: Colors.grey),
                        SizedBox(width: 12),
                        Text(
                          'Chargement des catégories...',
                          style: TextStyle(color: Colors.grey),
                        ),
                        Spacer(),
                        SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(strokeWidth: 2),
                        ),
                        SizedBox(width: 12),
                      ],
                    ),
                  ),
                ),
            const SizedBox(height: 16),

            // Description (optionnelle)
            TextFormField(
              controller: _descriptionController,
              decoration: const InputDecoration(
                labelText: 'Description (optionnelle)',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.notes),
              ),
              maxLines: 2,
            ),
            const SizedBox(height: 20),

            // Bouton d'enregistrement
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: _isLoading ? null : _addTransaction,
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppTheme.primaryColor,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                ),
                child:
                    _isLoading
                        ? const SizedBox(
                          height: 20,
                          width: 20,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor: AlwaysStoppedAnimation<Color>(
                              Colors.white,
                            ),
                          ),
                        )
                        : const Text(
                          'Enregistrer',
                          style: TextStyle(fontSize: 16, color: Colors.white),
                        ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  void dispose() {
    _nameController.dispose();
    _amountController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }
}
