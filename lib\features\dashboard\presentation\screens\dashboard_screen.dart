import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/providers/app_providers.dart';
import '../widgets/balance_card.dart';
import '../widgets/recent_transactions.dart';
import '../widgets/upcoming_tasks.dart';
import '../widgets/expense_chart.dart';
import '../widgets/budget_overview.dart';
import '../widgets/goal_overview.dart';
import '../widgets/floating_action_menu.dart';
import '../../../transactions/presentation/screens/transactions_screen.dart';
import '../../../budgets/presentation/screens/budgets_screen.dart';
import '../../../categories/presentation/screens/categories_screen.dart';
import '../../../debts/presentation/screens/debts_screen.dart';
import '../../../profile/presentation/screens/profile_screen.dart';

class DashboardScreen extends ConsumerStatefulWidget {
  const DashboardScreen({super.key});

  @override
  ConsumerState<DashboardScreen> createState() => _DashboardScreenState();
}

class _DashboardScreenState extends ConsumerState<DashboardScreen> {
  int _selectedIndex = 0;

  final List<Widget> _screens = [
    const _DashboardHomeScreen(),
    const TransactionsScreen(),
    const BudgetsScreen(),
    const CategoriesScreen(),
    const DebtsScreen(),
    const ProfileScreen(),
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: IndexedStack(index: _selectedIndex, children: _screens),
      floatingActionButton: _shouldShowFAB() 
          ? FloatingActionMenu(currentIndex: _selectedIndex)
          : null,
      bottomNavigationBar: Container(
        margin: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.blue,
          borderRadius: BorderRadius.circular(25),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              blurRadius: 20,
              offset: const Offset(0, 5),
            ),
          ],
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(25),
          child: BottomNavigationBar(
            currentIndex: _selectedIndex,
            onTap: (index) {
              setState(() {
                _selectedIndex = index;
              });
            },
            type: BottomNavigationBarType.fixed,
            backgroundColor: Colors.transparent,
            elevation: 0,
            selectedItemColor: Colors.black,
            unselectedItemColor: Colors.white,
            selectedFontSize: 12,
            unselectedFontSize: 10,
            items: [
              BottomNavigationBarItem(
                icon: _buildNavIcon(Icons.dashboard, 0),
                label: 'Tableau',
              ),
              BottomNavigationBarItem(
                icon: _buildNavIcon(Icons.receipt_long, 1),
                label: 'Transactions',
              ),
              BottomNavigationBarItem(
                icon: _buildNavIcon(Icons.account_balance_wallet, 2),
                label: 'Budgets',
              ),
              BottomNavigationBarItem(
                icon: _buildNavIcon(Icons.category, 3),
                label: 'Catégories',
              ),
              BottomNavigationBarItem(
                icon: _buildNavIcon(Icons.credit_card, 4),
                label: 'Dettes',
              ),
              BottomNavigationBarItem(
                icon: _buildNavIcon(Icons.person, 5),
                label: 'Profil',
              ),
            ],
          ),
        ),
      ),
    );
  }

  bool _shouldShowFAB() {
    // Afficher le FAB pour tous les écrans sauf le profil
    return _selectedIndex != 5;
  }

  Widget _buildNavIcon(IconData icon, int index) {
    final isSelected = _selectedIndex == index;
    return AnimatedContainer(
      duration: const Duration(milliseconds: 200),
      padding: EdgeInsets.all(isSelected ? 8 : 4),
      decoration: BoxDecoration(
        color: isSelected 
            ? Colors.white.withOpacity(0.2)
            : Colors.transparent,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Icon(
        icon,
        size: isSelected ? 26 : 24,
      ),
    );
  }


}

class _DashboardHomeScreen extends ConsumerWidget {
  const _DashboardHomeScreen();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final transactionService = ref.watch(transactionServiceProvider);
    final authService = ref.watch(authServiceProvider);

    return SafeArea(
      child: RefreshIndicator(
        onRefresh: () async {
          // Actualiser les données depuis les services
          await Future.delayed(const Duration(milliseconds: 500));
        },
        child: ListView(
          padding: const EdgeInsets.all(16),
          children: [
            // Carte de solde avec données réelles
            FutureBuilder<List<double>>(
              future: Future.wait([
                transactionService.getTotalBalance(
                  authService.currentUser?.id ?? '',
                ),
                Future<double>(() {
                  // Calculer le solde du mois précédent
                  final now = DateTime.now();
                  final currentMonth = DateTime(now.year, now.month, 1);
                  final previousMonth = DateTime(now.year, now.month - 1, 1);

                  final currentMonthBalance = transactionService
                      .calculateMonthlyBalance(
                        authService.currentUser?.id ?? '',
                        currentMonth,
                      );

                  final previousMonthBalance = transactionService
                      .calculateMonthlyBalance(
                        authService.currentUser?.id ?? '',
                        previousMonth,
                      );

                  // Éviter la division par zéro
                  if (previousMonthBalance == 0) return 0.0;

                  // Calculer la variation en pourcentage
                  final variation =
                      ((currentMonthBalance - previousMonthBalance) /
                          previousMonthBalance.abs()) *
                      100;
                  return variation;
                }),
              ]),
              builder: (context, snapshot) {
                final balance = snapshot.data?[0] ?? 0.0;
                final variation = snapshot.data?[1] ?? 0.0;
                return BalanceCard(balance: balance, variation: variation);
              },
            ),
            const SizedBox(height: 24),

            // Graphique des dépenses par catégorie
            const ExpenseChart(),
            const SizedBox(height: 24),

            // Aperçu des budgets
            const BudgetOverview(),
            const SizedBox(height: 24),

            // Aperçu des objectifs d'épargne
            const GoalOverview(),
            const SizedBox(height: 24),

            // Transactions récentes
            const RecentTransactions(),
            const SizedBox(height: 24),

            // Tâches à venir
            const UpcomingTasks(),
            const SizedBox(height: 24),
          ],
        ),
      ),
    );
  }
}
