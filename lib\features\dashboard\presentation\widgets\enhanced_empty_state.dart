import 'package:flutter/material.dart';
import '../../../../core/theme/app_theme.dart';

class EnhancedEmptyState extends StatefulWidget {
  final String title;
  final String description;
  final IconData icon;
  final String? actionText;
  final VoidCallback? onActionPressed;
  final Color? color;
  final Widget? illustration;

  const EnhancedEmptyState({
    super.key,
    required this.title,
    required this.description,
    required this.icon,
    this.actionText,
    this.onActionPressed,
    this.color,
    this.illustration,
  });

  @override
  State<EnhancedEmptyState> createState() => _EnhancedEmptyStateState();
}

class _EnhancedEmptyStateState extends State<EnhancedEmptyState>
    with TickerProviderStateMixin {
  late AnimationController _floatingController;
  late AnimationController _fadeController;
  late Animation<double> _floatingAnimation;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();

    _floatingController = AnimationController(
      duration: const Duration(seconds: 3),
      vsync: this,
    );

    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _floatingAnimation = Tween<double>(begin: 0, end: 10).animate(
      CurvedAnimation(parent: _floatingController, curve: Curves.easeInOut),
    );

    _fadeAnimation = Tween<double>(
      begin: 0,
      end: 1,
    ).animate(CurvedAnimation(parent: _fadeController, curve: Curves.easeOut));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(
      CurvedAnimation(parent: _fadeController, curve: Curves.easeOutCubic),
    );

    // Démarrer les animations
    _fadeController.forward();
    _floatingController.repeat(reverse: true);
  }

  @override
  void dispose() {
    _floatingController.dispose();
    _fadeController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final effectiveColor = widget.color ?? AppTheme.primaryColor;

    return AnimatedBuilder(
      animation: Listenable.merge([_fadeController, _floatingController]),
      builder: (context, child) {
        return FadeTransition(
          opacity: _fadeAnimation,
          child: SlideTransition(
            position: _slideAnimation,
            child: Center(
              child: Padding(
                padding: const EdgeInsets.all(32),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    // Illustration ou icône
                    Transform.translate(
                      offset: Offset(0, _floatingAnimation.value),
                      child:
                          widget.illustration ??
                          _buildDefaultIllustration(effectiveColor),
                    ),
                    const SizedBox(height: 32),

                    // Titre
                    Text(
                      widget.title,
                      style: TextStyle(
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                        color: Colors.grey[800],
                      ),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 12),

                    // Description
                    Text(
                      widget.description,
                      style: TextStyle(
                        fontSize: 16,
                        color: Colors.grey[600],
                        height: 1.5,
                      ),
                      textAlign: TextAlign.center,
                    ),

                    // Bouton d'action
                    if (widget.actionText != null &&
                        widget.onActionPressed != null) ...[
                      const SizedBox(height: 32),
                      _buildActionButton(effectiveColor),
                    ],
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildDefaultIllustration(Color color) {
    return Container(
      width: 120,
      height: 120,
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        shape: BoxShape.circle,
        border: Border.all(color: color.withOpacity(0.2), width: 2),
      ),
      child: Stack(
        alignment: Alignment.center,
        children: [
          // Cercles d'arrière-plan animés
          ...List.generate(3, (index) {
            return AnimatedBuilder(
              animation: _floatingController,
              builder: (context, child) {
                final delay = index * 0.3;
                final animationValue =
                    (_floatingController.value + delay) % 1.0;
                return Transform.scale(
                  scale: 0.5 + (animationValue * 0.5),
                  child: Container(
                    width: 80 - (index * 20),
                    height: 80 - (index * 20),
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: color.withOpacity(0.1 - (index * 0.03)),
                    ),
                  ),
                );
              },
            );
          }),

          // Icône principale
          Icon(widget.icon, size: 48, color: color),
        ],
      ),
    );
  }

  Widget _buildActionButton(Color color) {
    return TweenAnimationBuilder<double>(
      duration: const Duration(milliseconds: 300),
      tween: Tween(begin: 0, end: 1),
      builder: (context, value, child) {
        return Transform.scale(
          scale: value,
          child: ElevatedButton.icon(
            onPressed: widget.onActionPressed,
            icon: Icon(Icons.add_circle_outline, size: 20),
            label: Text(
              widget.actionText!,
              style: const TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
            ),
            style: ElevatedButton.styleFrom(
              backgroundColor: color,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(25),
              ),
              elevation: 4,
              shadowColor: color.withOpacity(0.3),
            ),
          ),
        );
      },
    );
  }
}

// États vides prédéfinis pour différentes sections
class EmptyStates {
  static Widget transactions({VoidCallback? onAddTransaction}) {
    return EnhancedEmptyState(
      title: 'Aucune transaction',
      description:
          'Commencez à suivre vos finances en ajoutant votre première transaction.',
      icon: Icons.receipt_long_outlined,
      actionText: 'Ajouter une transaction',
      onActionPressed: onAddTransaction,
      color: Colors.green,
    );
  }

  static Widget budgets({VoidCallback? onAddBudget}) {
    return EnhancedEmptyState(
      title: 'Aucun budget défini',
      description:
          'Créez des budgets pour mieux contrôler vos dépenses et atteindre vos objectifs financiers.',
      icon: Icons.account_balance_wallet_outlined,
      actionText: 'Créer un budget',
      onActionPressed: onAddBudget,
      color: Colors.blue,
    );
  }

  static Widget categories({VoidCallback? onAddCategory}) {
    return EnhancedEmptyState(
      title: 'Aucune catégorie',
      description:
          'Organisez vos transactions en créant des catégories personnalisées.',
      icon: Icons.category_outlined,
      actionText: 'Ajouter une catégorie',
      onActionPressed: onAddCategory,
      color: Colors.orange,
    );
  }

  static Widget debts({VoidCallback? onAddDebt}) {
    return EnhancedEmptyState(
      title: 'Aucune dette enregistrée',
      description:
          'Suivez vos dettes et remboursements pour une meilleure gestion financière.',
      icon: Icons.credit_card_outlined,
      actionText: 'Ajouter une dette',
      onActionPressed: onAddDebt,
      color: Colors.red,
    );
  }

  static Widget expenses() {
    return EnhancedEmptyState(
      title: 'Aucune dépense ce mois-ci',
      description:
          'Vos dépenses apparaîtront ici une fois que vous aurez ajouté des transactions.',
      icon: Icons.pie_chart_outline,
      color: Colors.purple,
    );
  }

  static Widget goals({VoidCallback? onAddGoal}) {
    return EnhancedEmptyState(
      title: 'Aucun objectif défini',
      description:
          'Fixez-vous des objectifs d\'épargne pour réaliser vos projets.',
      icon: Icons.flag_outlined,
      actionText: 'Définir un objectif',
      onActionPressed: onAddGoal,
      color: Colors.teal,
    );
  }
}
