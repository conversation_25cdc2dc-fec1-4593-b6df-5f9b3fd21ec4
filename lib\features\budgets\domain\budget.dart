import 'package:hive/hive.dart';

part 'budget.g.dart';

@HiveType(typeId: 2)
class Budget {
  @HiveField(0)
  final String id;
  
  @HiveField(1)
  final String userId;
  
  @HiveField(2)
  final String name;
  
  @HiveField(3)
  final String categoryId;
  
  @HiveField(4)
  final int allocatedAmount; // Amount in cents
  
  @HiveField(5)
  final int spentAmount; // Amount in cents
  
  @HiveField(6)
  final DateTime startDate;
  
  @HiveField(7)
  final DateTime endDate;
  
  @HiveField(8)
  final String? description;
  
  @HiveField(9)
  final int alertThreshold; // Percentage (0-100)
  
  @HiveField(10)
  final bool isActive;
  
  @HiveField(11)
  final DateTime createdAt;
  
  @HiveField(12)
  final DateTime updatedAt;

  Budget({
    required this.id,
    required this.userId,
    required this.name,
    required this.categoryId,
    required this.allocatedAmount,
    required this.spentAmount,
    required this.startDate,
    required this.endDate,
    this.description,
    required this.alertThreshold,
    required this.isActive,
    required this.createdAt,
    required this.updatedAt,
  });
  
  String get formattedAllocatedAmount {
    final amountInFcfa = allocatedAmount / 100;
    return '${amountInFcfa.toStringAsFixed(0)} FCFA';
  }
  
  String get formattedSpentAmount {
    final amountInFcfa = spentAmount / 100;
    return '${amountInFcfa.toStringAsFixed(0)} FCFA';
  }
  
  String get formattedRemainingAmount {
    final remaining = allocatedAmount - spentAmount;
    final amountInFcfa = remaining / 100;
    return '${amountInFcfa.toStringAsFixed(0)} FCFA';
  }
  
  double get usagePercentage {
    if (allocatedAmount == 0) return 0.0;
    return (spentAmount / allocatedAmount) * 100;
  }
  
  int get remainingAmount => allocatedAmount - spentAmount;
  
  bool get isOverBudget => spentAmount > allocatedAmount;
  
  bool get isNearLimit => usagePercentage >= alertThreshold;
  
  BudgetStatus get status {
    if (isOverBudget) return BudgetStatus.exceeded;
    if (isNearLimit) return BudgetStatus.warning;
    return BudgetStatus.onTrack;
  }
  
  int get daysRemaining {
    final now = DateTime.now();
    if (now.isAfter(endDate)) return 0;
    return endDate.difference(now).inDays;
  }
  
  bool get isExpired {
    return DateTime.now().isAfter(endDate);
  }
  
  Budget copyWith({
    String? id,
    String? userId,
    String? name,
    String? categoryId,
    int? allocatedAmount,
    int? spentAmount,
    DateTime? startDate,
    DateTime? endDate,
    String? description,
    int? alertThreshold,
    bool? isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Budget(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      name: name ?? this.name,
      categoryId: categoryId ?? this.categoryId,
      allocatedAmount: allocatedAmount ?? this.allocatedAmount,
      spentAmount: spentAmount ?? this.spentAmount,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      description: description ?? this.description,
      alertThreshold: alertThreshold ?? this.alertThreshold,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
  
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'userId': userId,
      'name': name,
      'categoryId': categoryId,
      'allocatedAmount': allocatedAmount,
      'spentAmount': spentAmount,
      'startDate': startDate.toIso8601String(),
      'endDate': endDate.toIso8601String(),
      'description': description,
      'alertThreshold': alertThreshold,
      'isActive': isActive,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }
  
  factory Budget.fromJson(Map<String, dynamic> json) {
    return Budget(
      id: json['id'],
      userId: json['userId'],
      name: json['name'],
      categoryId: json['categoryId'],
      allocatedAmount: json['allocatedAmount'],
      spentAmount: json['spentAmount'],
      startDate: DateTime.parse(json['startDate']),
      endDate: DateTime.parse(json['endDate']),
      description: json['description'],
      alertThreshold: json['alertThreshold'],
      isActive: json['isActive'],
      createdAt: DateTime.parse(json['createdAt']),
      updatedAt: DateTime.parse(json['updatedAt']),
    );
  }
  
  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Budget && other.id == id;
  }
  
  @override
  int get hashCode => id.hashCode;
  
  @override
  String toString() {
    return 'Budget(id: $id, name: $name, allocated: $formattedAllocatedAmount, spent: $formattedSpentAmount, status: $status)';
  }
}

enum BudgetStatus {
  onTrack,
  warning,
  exceeded,
}

extension BudgetStatusExtension on BudgetStatus {
  String get displayName {
    switch (this) {
      case BudgetStatus.onTrack:
        return 'En cours';
      case BudgetStatus.warning:
        return 'Attention';
      case BudgetStatus.exceeded:
        return 'Dépassé';
    }
  }
  
  String get emoji {
    switch (this) {
      case BudgetStatus.onTrack:
        return '✅';
      case BudgetStatus.warning:
        return '⚠️';
      case BudgetStatus.exceeded:
        return '🚨';
    }
  }
}