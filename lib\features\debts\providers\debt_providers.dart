import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../domain/debt.dart';
import '../domain/repayment.dart';
import '../services/debt_service.dart';
import '../../../core/providers/app_providers.dart';

// Provider pour le service de dettes
final debtServiceProvider = Provider<DebtService>((ref) {
  final storageService = ref.read(storageServiceProvider);
  return DebtService(storageService);
});

// Provider pour toutes les dettes de l'utilisateur
final userDebtsProvider = FutureProvider<List<Debt>>((ref) async {
  final debtService = ref.read(debtServiceProvider);
  final authService = ref.read(authServiceProvider);
  final userId = authService.currentUser?.id;
  
  return debtService.getUserDebts(userId);
});

// Provider pour les dettes actives
final activeDebtsProvider = FutureProvider<List<Debt>>((ref) async {
  final debtService = ref.read(debtServiceProvider);
  final authService = ref.read(authServiceProvider);
  final userId = authService.currentUser?.id;
  
  return debtService.getActiveDebts(userId);
});

// Provider pour les dettes remboursées
final paidDebtsProvider = FutureProvider<List<Debt>>((ref) async {
  final debtService = ref.read(debtServiceProvider);
  final authService = ref.read(authServiceProvider);
  final userId = authService.currentUser?.id;
  
  return debtService.getPaidDebts(userId);
});

// Provider pour les dettes en retard
final overdueDebtsProvider = FutureProvider<List<Debt>>((ref) async {
  final debtService = ref.read(debtServiceProvider);
  final authService = ref.read(authServiceProvider);
  final userId = authService.currentUser?.id;
  
  return debtService.getOverdueDebts(userId);
});

// Provider pour les dettes qui arrivent à échéance bientôt
final upcomingDueDebtsProvider = FutureProvider<List<Debt>>((ref) async {
  final debtService = ref.read(debtServiceProvider);
  final authService = ref.read(authServiceProvider);
  final userId = authService.currentUser?.id;
  
  return debtService.getUpcomingDueDebts(userId);
});

// Provider pour une dette spécifique
final debtProvider = FutureProvider.family<Debt?, String>((ref, debtId) async {
  final debtService = ref.read(debtServiceProvider);
  return debtService.getDebtById(debtId);
});

// Provider pour les remboursements d'une dette
final debtRepaymentsProvider = FutureProvider.family<List<Repayment>, String>((ref, debtId) async {
  final debtService = ref.read(debtServiceProvider);
  return debtService.getRepaymentsByDebtId(debtId);
});

// Provider pour le résumé d'une dette
final debtSummaryProvider = FutureProvider.family<DebtSummary, String>((ref, debtId) async {
  final debtService = ref.read(debtServiceProvider);
  return debtService.getDebtSummary(debtId);
});

// Provider pour le total des dettes actives
final totalActiveDebtsProvider = FutureProvider<int>((ref) async {
  final debtService = ref.read(debtServiceProvider);
  final authService = ref.read(authServiceProvider);
  final userId = authService.currentUser?.id;
  
  return debtService.getTotalActiveDebts(userId);
});

// Provider pour le total remboursé ce mois
final totalPaidThisMonthProvider = FutureProvider<int>((ref) async {
  final debtService = ref.read(debtServiceProvider);
  final authService = ref.read(authServiceProvider);
  final userId = authService.currentUser?.id;
  
  return debtService.getTotalPaidThisMonth(userId);
});

// Provider pour les statistiques générales des dettes
final debtStatsProvider = FutureProvider<DebtStats>((ref) async {
  final debtService = ref.read(debtServiceProvider);
  final authService = ref.read(authServiceProvider);
  final userId = authService.currentUser?.id;
  
  final activeDebts = debtService.getActiveDebts(userId);
  final paidDebts = debtService.getPaidDebts(userId);
  final overdueDebts = debtService.getOverdueDebts(userId);
  final totalActive = debtService.getTotalActiveDebts(userId);
  final totalPaidThisMonth = debtService.getTotalPaidThisMonth(userId);
  
  return DebtStats(
    totalActiveDebts: activeDebts.length,
    totalPaidDebts: paidDebts.length,
    totalOverdueDebts: overdueDebts.length,
    totalActiveAmount: totalActive,
    totalPaidThisMonth: totalPaidThisMonth,
  );
});

// Classe pour les statistiques des dettes
class DebtStats {
  final int totalActiveDebts;
  final int totalPaidDebts;
  final int totalOverdueDebts;
  final int totalActiveAmount;
  final int totalPaidThisMonth;

  DebtStats({
    required this.totalActiveDebts,
    required this.totalPaidDebts,
    required this.totalOverdueDebts,
    required this.totalActiveAmount,
    required this.totalPaidThisMonth,
  });

  String get formattedTotalActiveAmount {
    final amountInFcfa = totalActiveAmount / 100;
    return '${amountInFcfa.toStringAsFixed(0)} FCFA';
  }

  String get formattedTotalPaidThisMonth {
    final amountInFcfa = totalPaidThisMonth / 100;
    return '${amountInFcfa.toStringAsFixed(0)} FCFA';
  }
}

// Provider pour rafraîchir les données
final refreshDebtsProvider = StateProvider<int>((ref) => 0);

// Méthode utilitaire pour rafraîchir tous les providers de dettes
void refreshAllDebtProviders(WidgetRef ref) {
  ref.invalidate(userDebtsProvider);
  ref.invalidate(activeDebtsProvider);
  ref.invalidate(paidDebtsProvider);
  ref.invalidate(overdueDebtsProvider);
  ref.invalidate(upcomingDueDebtsProvider);
  ref.invalidate(totalActiveDebtsProvider);
  ref.invalidate(totalPaidThisMonthProvider);
  ref.invalidate(debtStatsProvider);
  
  // Incrémenter le compteur de rafraîchissement
  ref.read(refreshDebtsProvider.notifier).state++;
}
