# Configuration des Informations de l'Application

## Vue d'ensemble
Ce document décrit la configuration des informations de version et d'auteur pour l'application Family Budgets.

## Informations configurées
- **Version**: 1.0.0
- **Auteur**: <PERSON>oro B<PERSON> Menan V.
- **Nom de l'application**: Family Budgets

## Fichiers modifiés

### 1. Fichier de constantes centralisé
- **Fichier**: `lib/core/constants/app_info.dart`
- **Description**: Contient toutes les informations de l'application (version, auteur, nom, etc.)
- **Avantages**: Centralisation des données, facilite la maintenance

### 2. Configuration du projet
- **Fichier**: `pubspec.yaml`
  - Mise à jour de la description avec le nom de l'auteur
  - Version maintenue à 1.0.0+1

### 3. Documentation
- **Fichier**: `README.md`
  - Ajout des informations de version et d'auteur
  - Description complète du projet
  - Instructions d'installation et d'utilisation

### 4. Interface utilisateur
- **<PERSON>chier**: `lib/features/profile/presentation/screens/profile_screen.dart`
  - Affichage de la version et de l'auteur dans l'écran de profil
  - Utilisation des constantes du fichier `app_info.dart`

### 5. Écrans de l'application
Mise à jour pour utiliser les constantes centralisées :
- `lib/features/splash/presentation/screens/splash_screen.dart`
- `lib/features/auth/presentation/screens/splash_screen.dart`
- `lib/features/auth/presentation/screens/login_screen.dart`
- `lib/app/app.dart`

## Utilisation des constantes

```dart
import '../../../../core/constants/app_info.dart';

// Utilisation du nom de l'application
Text(AppInfo.appName)

// Utilisation de la version
Text('Version: ${AppInfo.version}')

// Utilisation de l'auteur
Text('Développé par: ${AppInfo.author}')

// Copyright complet
Text(AppInfo.copyright)
```

## Avantages de cette approche

1. **Centralisation**: Toutes les informations sont dans un seul fichier
2. **Maintenance facile**: Modification en un seul endroit
3. **Cohérence**: Même information partout dans l'application
4. **Évolutivité**: Facile d'ajouter de nouvelles informations

## Mise à jour future

Pour mettre à jour la version ou les informations :
1. Modifier le fichier `lib/core/constants/app_info.dart`
2. Mettre à jour `pubspec.yaml` si nécessaire
3. L'application utilisera automatiquement les nouvelles valeurs

## Localisation dans l'interface

- **Écran de profil**: Version et auteur affichés en bas
- **Écrans de démarrage**: Nom de l'application
- **Écran de connexion**: Nom de l'application
- **Configuration de l'app**: Titre de l'application