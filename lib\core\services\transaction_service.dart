import 'package:uuid/uuid.dart';
import '../../features/transactions/domain/transaction.dart';
import 'storage_service.dart';

class TransactionService {
  final StorageService _storage;
  final Uuid _uuid = const Uuid();

  TransactionService(this._storage);

  // Create new transaction
  Future<TransactionResult> createTransaction({
    required String userId,
    required String categoryId,
    required String accountId,
    required String name,
    required int amount,
    required bool isExpense,
    String? description,
    DateTime? date,
  }) async {
    try {
      final transaction = Transaction(
        id: _uuid.v4(),
        userId: userId,
        categoryId: categoryId,
        accountId: accountId,
        name: name,
        amount: amount,
        isExpense: isExpense,
        description: description,
        date: date ?? DateTime.now(),
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      await _storage.saveTransaction(transaction);
      return TransactionResult.success(transaction);
    } catch (e) {
      return TransactionResult.failure(
        'Erreur lors de la création: ${e.toString()}',
      );
    }
  }

  // Update transaction
  Future<TransactionResult> updateTransaction({
    required String transactionId,
    String? name,
    int? amount,
    bool? isExpense,
    String? description,
    DateTime? date,
    String? categoryId,
    String? accountId,
  }) async {
    try {
      final existingTransaction = _storage.getTransaction(transactionId);
      if (existingTransaction == null) {
        return TransactionResult.failure('Transaction non trouvée');
      }

      final updatedTransaction = existingTransaction.copyWith(
        name: name ?? existingTransaction.name,
        amount: amount ?? existingTransaction.amount,
        isExpense: isExpense ?? existingTransaction.isExpense,
        description: description ?? existingTransaction.description,
        date: date ?? existingTransaction.date,
        categoryId: categoryId ?? existingTransaction.categoryId,
        accountId: accountId ?? existingTransaction.accountId,
        updatedAt: DateTime.now(),
      );

      await _storage.saveTransaction(updatedTransaction);
      return TransactionResult.success(updatedTransaction);
    } catch (e) {
      return TransactionResult.failure(
        'Erreur lors de la mise à jour: ${e.toString()}',
      );
    }
  }

  // Delete transaction
  Future<TransactionResult> deleteTransaction(String transactionId) async {
    try {
      final transaction = _storage.getTransaction(transactionId);
      if (transaction == null) {
        return TransactionResult.failure('Transaction non trouvée');
      }

      await _storage.deleteTransaction(transactionId);
      return TransactionResult.success(transaction);
    } catch (e) {
      return TransactionResult.failure(
        'Erreur lors de la suppression: ${e.toString()}',
      );
    }
  }

  // Get transactions by user
  List<Transaction> getTransactionsByUser(String userId) {
    return _storage.getTransactionsByUserId(userId)
      ..sort((a, b) => b.date.compareTo(a.date));
  }

  // Get recent transactions
  List<Transaction> getRecentTransactions(String userId, {int limit = 10}) {
    final transactions = getTransactionsByUser(userId);
    return transactions.take(limit).toList();
  }

  // Get transactions by date range
  List<Transaction> getTransactionsByDateRange(
    String userId,
    DateTime startDate,
    DateTime endDate,
  ) {
    final transactions = getTransactionsByUser(userId);
    return transactions.where((transaction) {
      return transaction.date.isAfter(
            startDate.subtract(const Duration(days: 1)),
          ) &&
          transaction.date.isBefore(endDate.add(const Duration(days: 1)));
    }).toList();
  }

  // Get transactions by category
  List<Transaction> getTransactionsByCategory(
    String userId,
    String categoryId,
  ) {
    final transactions = getTransactionsByUser(userId);
    return transactions
        .where((transaction) => transaction.categoryId == categoryId)
        .toList();
  }

  // Calculate total balance
  int calculateTotalBalance(String userId) {
    final transactions = getTransactionsByUser(userId);
    int balance = 0;

    for (final transaction in transactions) {
      if (transaction.isExpense) {
        balance -= transaction.amount;
      } else {
        balance += transaction.amount;
      }
    }

    return balance;
  }

  // Calculate monthly balance
  int calculateMonthlyBalance(String userId, DateTime month) {
    final startOfMonth = DateTime(month.year, month.month, 1);
    final endOfMonth = DateTime(month.year, month.month + 1, 0);

    final monthlyTransactions = getTransactionsByDateRange(
      userId,
      startOfMonth,
      endOfMonth,
    );

    int balance = 0;
    for (final transaction in monthlyTransactions) {
      if (transaction.isExpense) {
        balance -= transaction.amount;
      } else {
        balance += transaction.amount;
      }
    }

    return balance;
  }

  // Calculate total income
  int calculateTotalIncome(
    String userId, {
    DateTime? startDate,
    DateTime? endDate,
  }) {
    List<Transaction> transactions;

    if (startDate != null && endDate != null) {
      transactions = getTransactionsByDateRange(userId, startDate, endDate);
    } else {
      transactions = getTransactionsByUser(userId);
    }

    return transactions
        .where((transaction) => !transaction.isExpense)
        .fold(0, (sum, transaction) => sum + transaction.amount);
  }

  // Calculate total expenses
  int calculateTotalExpenses(
    String userId, {
    DateTime? startDate,
    DateTime? endDate,
  }) {
    List<Transaction> transactions;

    if (startDate != null && endDate != null) {
      transactions = getTransactionsByDateRange(userId, startDate, endDate);
    } else {
      transactions = getTransactionsByUser(userId);
    }

    return transactions
        .where((transaction) => transaction.isExpense)
        .fold(0, (sum, transaction) => sum + transaction.amount);
  }

  // Get expense breakdown by category
  Map<String, int> getExpenseBreakdownByCategory(
    String userId, {
    DateTime? startDate,
    DateTime? endDate,
  }) {
    List<Transaction> transactions;

    if (startDate != null && endDate != null) {
      transactions = getTransactionsByDateRange(userId, startDate, endDate);
    } else {
      transactions = getTransactionsByUser(userId);
    }

    final expenses = transactions.where((transaction) => transaction.isExpense);
    final breakdown = <String, int>{};

    for (final expense in expenses) {
      breakdown[expense.categoryId] =
          (breakdown[expense.categoryId] ?? 0) + expense.amount;
    }

    return breakdown;
  }

  // Get monthly spending trend
  Map<DateTime, int> getMonthlySpendingTrend(
    String userId,
    int numberOfMonths,
  ) {
    final now = DateTime.now();
    final trend = <DateTime, int>{};

    for (int i = numberOfMonths - 1; i >= 0; i--) {
      final month = DateTime(now.year, now.month - i, 1);
      final monthlyExpenses = calculateTotalExpenses(
        userId,
        startDate: month,
        endDate: DateTime(month.year, month.month + 1, 0),
      );
      trend[month] = monthlyExpenses;
    }

    return trend;
  }

  // Get total balance for user
  Future<double> getTotalBalance(String userId) async {
    final transactions = getTransactionsByUser(userId);
    double balance = 0.0;

    for (final transaction in transactions) {
      if (transaction.isExpense) {
        balance -= transaction.amount / 100.0; // Convert from cents to currency
      } else {
        balance += transaction.amount / 100.0; // Convert from cents to currency
      }
    }

    return balance;
  }

  // Search transactions
  List<Transaction> searchTransactions(String userId, String query) {
    final transactions = getTransactionsByUser(userId);
    final lowercaseQuery = query.toLowerCase();

    return transactions.where((transaction) {
      return transaction.name.toLowerCase().contains(lowercaseQuery) ||
          (transaction.description?.toLowerCase().contains(lowercaseQuery) ??
              false);
    }).toList();
  }
}

class TransactionResult {
  final bool success;
  final String? message;
  final Transaction? transaction;

  TransactionResult._(this.success, this.message, this.transaction);

  factory TransactionResult.success(Transaction transaction) {
    return TransactionResult._(true, null, transaction);
  }

  factory TransactionResult.failure(String message) {
    return TransactionResult._(false, message, null);
  }
}
