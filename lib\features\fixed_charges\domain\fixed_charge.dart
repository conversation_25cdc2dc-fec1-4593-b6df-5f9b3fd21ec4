import 'package:hive/hive.dart';

part 'fixed_charge.g.dart';

@HiveType(typeId: 13)
class FixedCharge {
  @HiveField(0)
  final String id;
  
  @HiveField(1)
  final String userId;
  
  @HiveField(2)
  final String name;
  
  @HiveField(3)
  final int amount; // Montant en centimes
  
  @HiveField(4)
  final int dayOfMonth; // <PERSON><PERSON> (1-31)
  
  @HiveField(5)
  final ChargeRecurrence recurrence;
  
  @HiveField(6)
  final String? categoryId;
  
  @HiveField(7)
  final String? description;
  
  @HiveField(8)
  final bool isActive;
  
  @HiveField(9)
  final bool reminderEnabled;
  
  @HiveField(10)
  final int reminderDaysBefore; // Nombre de jours avant pour le rappel
  
  @HiveField(11)
  final ChargeType type;
  
  @HiveField(12)
  final DateTime createdAt;
  
  @HiveField(13)
  final DateTime updatedAt;
  
  @HiveField(14)
  final DateTime? lastPaidDate;

  FixedCharge({
    required this.id,
    required this.userId,
    required this.name,
    required this.amount,
    required this.dayOfMonth,
    required this.recurrence,
    this.categoryId,
    this.description,
    required this.isActive,
    required this.reminderEnabled,
    required this.reminderDaysBefore,
    required this.type,
    required this.createdAt,
    required this.updatedAt,
    this.lastPaidDate,
  });

  // Getters calculés
  String get formattedAmount {
    final amountInFcfa = amount / 100;
    return '${amountInFcfa.toStringAsFixed(0)} FCFA';
  }

  DateTime get nextDueDate {
    final now = DateTime.now();
    DateTime nextDate;

    switch (recurrence) {
      case ChargeRecurrence.monthly:
        nextDate = DateTime(now.year, now.month, dayOfMonth);
        if (nextDate.isBefore(now) || nextDate.isAtSameMomentAs(now)) {
          nextDate = DateTime(now.year, now.month + 1, dayOfMonth);
        }
        break;
      case ChargeRecurrence.weekly:
        // Pour les charges hebdomadaires, on utilise dayOfMonth comme jour de la semaine (1=lundi, 7=dimanche)
        final daysUntilNext = (dayOfMonth - now.weekday) % 7;
        nextDate = now.add(Duration(days: daysUntilNext == 0 ? 7 : daysUntilNext));
        break;
      case ChargeRecurrence.quarterly:
        nextDate = DateTime(now.year, now.month, dayOfMonth);
        while (nextDate.isBefore(now) || nextDate.isAtSameMomentAs(now)) {
          nextDate = DateTime(nextDate.year, nextDate.month + 3, dayOfMonth);
        }
        break;
      case ChargeRecurrence.yearly:
        nextDate = DateTime(now.year, now.month, dayOfMonth);
        if (nextDate.isBefore(now) || nextDate.isAtSameMomentAs(now)) {
          nextDate = DateTime(now.year + 1, now.month, dayOfMonth);
        }
        break;
    }

    return nextDate;
  }

  bool get isOverdue {
    if (!isActive) return false;
    final now = DateTime.now();
    final dueDate = nextDueDate;
    return now.isAfter(dueDate);
  }

  bool get isDueSoon {
    if (!isActive || !reminderEnabled) return false;
    final now = DateTime.now();
    final dueDate = nextDueDate;
    final reminderDate = dueDate.subtract(Duration(days: reminderDaysBefore));
    return now.isAfter(reminderDate) && now.isBefore(dueDate);
  }

  int get daysUntilDue {
    final now = DateTime.now();
    final dueDate = nextDueDate;
    return dueDate.difference(now).inDays;
  }

  ChargeStatus get status {
    if (!isActive) return ChargeStatus.inactive;
    if (isOverdue) return ChargeStatus.overdue;
    if (isDueSoon) return ChargeStatus.dueSoon;
    return ChargeStatus.pending;
  }

  FixedCharge copyWith({
    String? id,
    String? userId,
    String? name,
    int? amount,
    int? dayOfMonth,
    ChargeRecurrence? recurrence,
    String? categoryId,
    String? description,
    bool? isActive,
    bool? reminderEnabled,
    int? reminderDaysBefore,
    ChargeType? type,
    DateTime? createdAt,
    DateTime? updatedAt,
    DateTime? lastPaidDate,
  }) {
    return FixedCharge(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      name: name ?? this.name,
      amount: amount ?? this.amount,
      dayOfMonth: dayOfMonth ?? this.dayOfMonth,
      recurrence: recurrence ?? this.recurrence,
      categoryId: categoryId ?? this.categoryId,
      description: description ?? this.description,
      isActive: isActive ?? this.isActive,
      reminderEnabled: reminderEnabled ?? this.reminderEnabled,
      reminderDaysBefore: reminderDaysBefore ?? this.reminderDaysBefore,
      type: type ?? this.type,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      lastPaidDate: lastPaidDate ?? this.lastPaidDate,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'userId': userId,
      'name': name,
      'amount': amount,
      'dayOfMonth': dayOfMonth,
      'recurrence': recurrence.name,
      'categoryId': categoryId,
      'description': description,
      'isActive': isActive,
      'reminderEnabled': reminderEnabled,
      'reminderDaysBefore': reminderDaysBefore,
      'type': type.name,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'lastPaidDate': lastPaidDate?.toIso8601String(),
    };
  }

  factory FixedCharge.fromJson(Map<String, dynamic> json) {
    return FixedCharge(
      id: json['id'],
      userId: json['userId'],
      name: json['name'],
      amount: json['amount'],
      dayOfMonth: json['dayOfMonth'],
      recurrence: ChargeRecurrence.values.firstWhere((e) => e.name == json['recurrence']),
      categoryId: json['categoryId'],
      description: json['description'],
      isActive: json['isActive'],
      reminderEnabled: json['reminderEnabled'],
      reminderDaysBefore: json['reminderDaysBefore'],
      type: ChargeType.values.firstWhere((e) => e.name == json['type']),
      createdAt: DateTime.parse(json['createdAt']),
      updatedAt: DateTime.parse(json['updatedAt']),
      lastPaidDate: json['lastPaidDate'] != null ? DateTime.parse(json['lastPaidDate']) : null,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is FixedCharge && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'FixedCharge(id: $id, name: $name, amount: $formattedAmount, recurrence: ${recurrence.displayName})';
  }
}

@HiveType(typeId: 14)
enum ChargeRecurrence {
  @HiveField(0)
  weekly, // Hebdomadaire
  
  @HiveField(1)
  monthly, // Mensuelle
  
  @HiveField(2)
  quarterly, // Trimestrielle
  
  @HiveField(3)
  yearly, // Annuelle
}

@HiveType(typeId: 15)
enum ChargeType {
  @HiveField(0)
  housing, // Logement (loyer, charges...)
  
  @HiveField(1)
  utilities, // Services publics (électricité, eau, gaz...)
  
  @HiveField(2)
  insurance, // Assurances
  
  @HiveField(3)
  subscription, // Abonnements
  
  @HiveField(4)
  loan, // Prêts/Crédits
  
  @HiveField(5)
  other, // Autres
}

@HiveType(typeId: 16)
enum ChargeStatus {
  @HiveField(0)
  pending, // En attente
  
  @HiveField(1)
  dueSoon, // Échéance proche
  
  @HiveField(2)
  overdue, // En retard
  
  @HiveField(3)
  inactive, // Inactive
}

extension ChargeRecurrenceExtension on ChargeRecurrence {
  String get displayName {
    switch (this) {
      case ChargeRecurrence.weekly:
        return 'Hebdomadaire';
      case ChargeRecurrence.monthly:
        return 'Mensuelle';
      case ChargeRecurrence.quarterly:
        return 'Trimestrielle';
      case ChargeRecurrence.yearly:
        return 'Annuelle';
    }
  }

  String get emoji {
    switch (this) {
      case ChargeRecurrence.weekly:
        return '📅';
      case ChargeRecurrence.monthly:
        return '🗓️';
      case ChargeRecurrence.quarterly:
        return '📆';
      case ChargeRecurrence.yearly:
        return '🗓️';
    }
  }
}

extension ChargeTypeExtension on ChargeType {
  String get displayName {
    switch (this) {
      case ChargeType.housing:
        return 'Logement';
      case ChargeType.utilities:
        return 'Services publics';
      case ChargeType.insurance:
        return 'Assurances';
      case ChargeType.subscription:
        return 'Abonnements';
      case ChargeType.loan:
        return 'Prêts/Crédits';
      case ChargeType.other:
        return 'Autres';
    }
  }

  String get emoji {
    switch (this) {
      case ChargeType.housing:
        return '🏠';
      case ChargeType.utilities:
        return '⚡';
      case ChargeType.insurance:
        return '🛡️';
      case ChargeType.subscription:
        return '📺';
      case ChargeType.loan:
        return '🏦';
      case ChargeType.other:
        return '📋';
    }
  }
}

extension ChargeStatusExtension on ChargeStatus {
  String get displayName {
    switch (this) {
      case ChargeStatus.pending:
        return 'En attente';
      case ChargeStatus.dueSoon:
        return 'Échéance proche';
      case ChargeStatus.overdue:
        return 'En retard';
      case ChargeStatus.inactive:
        return 'Inactive';
    }
  }

  String get emoji {
    switch (this) {
      case ChargeStatus.pending:
        return '⏳';
      case ChargeStatus.dueSoon:
        return '⚠️';
      case ChargeStatus.overdue:
        return '🔴';
      case ChargeStatus.inactive:
        return '⏸️';
    }
  }
}
