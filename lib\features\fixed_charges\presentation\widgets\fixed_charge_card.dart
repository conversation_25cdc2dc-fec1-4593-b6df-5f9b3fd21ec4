import 'package:flutter/material.dart';
import '../../domain/fixed_charge.dart';

class FixedChargeCard extends StatelessWidget {
  final FixedCharge charge;
  final VoidCallback? onTap;
  final VoidCallback? onMarkAsPaid;
  final VoidCallback? onToggleStatus;

  const FixedChargeCard({
    super.key,
    required this.charge,
    this.onTap,
    this.onMarkAsPaid,
    this.onToggleStatus,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Card(
      elevation: 2,
      margin: EdgeInsets.zero,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // En-tête avec nom et statut
              Row(
                children: [
                  // Icône du type de charge
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: _getStatusColor(charge.status).withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Text(
                      charge.type.emoji,
                      style: const TextStyle(fontSize: 20),
                    ),
                  ),
                  const SizedBox(width: 12),
                  
                  // Informations principales
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          charge.name,
                          style: theme.textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          charge.type.displayName,
                          style: theme.textTheme.bodyMedium?.copyWith(
                            color: Colors.grey[600],
                          ),
                        ),
                      ],
                    ),
                  ),
                  
                  // Badge de statut
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: _getStatusColor(charge.status),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(
                          charge.status.emoji,
                          style: const TextStyle(fontSize: 12),
                        ),
                        const SizedBox(width: 4),
                        Text(
                          charge.status.displayName,
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 12,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              
              const SizedBox(height: 16),
              
              // Montant et récurrence
              Row(
                children: [
                  Expanded(
                    child: _buildInfoItem(
                      context,
                      'Montant',
                      charge.formattedAmount,
                      Colors.blue,
                      Icons.attach_money,
                    ),
                  ),
                  Expanded(
                    child: _buildInfoItem(
                      context,
                      'Récurrence',
                      charge.recurrence.displayName,
                      Colors.green,
                      Icons.repeat,
                    ),
                  ),
                  Expanded(
                    child: _buildInfoItem(
                      context,
                      'Prochaine échéance',
                      _formatDate(charge.nextDueDate),
                      charge.isOverdue ? Colors.red : Colors.orange,
                      Icons.schedule,
                    ),
                  ),
                ],
              ),
              
              const SizedBox(height: 16),
              
              // Informations supplémentaires
              Row(
                children: [
                  // Jour de prélèvement
                  Expanded(
                    child: Row(
                      children: [
                        Icon(
                          Icons.calendar_today,
                          size: 16,
                          color: Colors.grey[600],
                        ),
                        const SizedBox(width: 4),
                        Text(
                          _getPaymentDayText(),
                          style: theme.textTheme.bodySmall?.copyWith(
                            color: Colors.grey[600],
                          ),
                        ),
                      ],
                    ),
                  ),
                  
                  // Rappel
                  if (charge.reminderEnabled)
                    Expanded(
                      child: Row(
                        children: [
                          Icon(
                            Icons.notifications,
                            size: 16,
                            color: Colors.grey[600],
                          ),
                          const SizedBox(width: 4),
                          Text(
                            '${charge.reminderDaysBefore}j avant',
                            style: theme.textTheme.bodySmall?.copyWith(
                              color: Colors.grey[600],
                            ),
                          ),
                        ],
                      ),
                    ),
                  
                  // Statut actif/inactif
                  Expanded(
                    child: Row(
                      children: [
                        Icon(
                          charge.isActive ? Icons.check_circle : Icons.pause_circle,
                          size: 16,
                          color: charge.isActive ? Colors.green : Colors.grey,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          charge.isActive ? 'Active' : 'Inactive',
                          style: theme.textTheme.bodySmall?.copyWith(
                            color: charge.isActive ? Colors.green : Colors.grey,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              
              // Boutons d'action
              if (charge.isActive) ...[
                const SizedBox(height: 16),
                Row(
                  children: [
                    if (charge.status == ChargeStatus.pending || charge.status == ChargeStatus.overdue)
                      Expanded(
                        child: ElevatedButton.icon(
                          onPressed: onMarkAsPaid,
                          icon: const Icon(Icons.payment, size: 16),
                          label: const Text('Marquer payée'),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.green,
                            foregroundColor: Colors.white,
                          ),
                        ),
                      ),
                    if (charge.status == ChargeStatus.pending || charge.status == ChargeStatus.overdue)
                      const SizedBox(width: 8),
                    Expanded(
                      child: OutlinedButton.icon(
                        onPressed: onToggleStatus,
                        icon: Icon(
                          charge.isActive ? Icons.pause : Icons.play_arrow,
                          size: 16,
                        ),
                        label: Text(charge.isActive ? 'Désactiver' : 'Activer'),
                      ),
                    ),
                  ],
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildInfoItem(
    BuildContext context,
    String label,
    String value,
    Color color,
    IconData icon,
  ) {
    final theme = Theme.of(context);
    
    return Column(
      children: [
        Icon(icon, color: color, size: 16),
        const SizedBox(height: 4),
        Text(
          label,
          style: theme.textTheme.bodySmall?.copyWith(
            color: Colors.grey[600],
            fontWeight: FontWeight.w500,
          ),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 2),
        Text(
          value,
          style: theme.textTheme.bodySmall?.copyWith(
            color: color,
            fontWeight: FontWeight.bold,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Color _getStatusColor(ChargeStatus status) {
    switch (status) {
      case ChargeStatus.pending:
        return Colors.blue;
      case ChargeStatus.dueSoon:
        return Colors.orange;
      case ChargeStatus.overdue:
        return Colors.red;
      case ChargeStatus.inactive:
        return Colors.grey;
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day.toString().padLeft(2, '0')}/'
           '${date.month.toString().padLeft(2, '0')}';
  }

  String _getPaymentDayText() {
    switch (charge.recurrence) {
      case ChargeRecurrence.weekly:
        final days = ['Lun', 'Mar', 'Mer', 'Jeu', 'Ven', 'Sam', 'Dim'];
        return days[charge.dayOfMonth - 1];
      case ChargeRecurrence.monthly:
      case ChargeRecurrence.quarterly:
      case ChargeRecurrence.yearly:
        return 'Le ${charge.dayOfMonth}';
    }
  }
}
