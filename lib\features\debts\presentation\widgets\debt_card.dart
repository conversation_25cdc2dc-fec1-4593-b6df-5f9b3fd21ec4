import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../domain/debt.dart';
import '../../providers/debt_providers.dart';

class DebtCard extends ConsumerWidget {
  final Debt debt;
  final VoidCallback? onTap;

  const DebtCard({super.key, required this.debt, this.onTap});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);
    final summaryAsync = ref.watch(debtSummaryProvider(debt.id));

    return Card(
      elevation: 2,
      margin: EdgeInsets.zero,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // En-tête avec créancier et statut
              Row(
                children: [
                  // Icône du type de dette
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: _getStatusColor(
                        debt.status,
                      ).withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Text(
                      debt.type.emoji,
                      style: const TextStyle(fontSize: 20),
                    ),
                  ),
                  const SizedBox(width: 12),

                  // Informations principales
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          debt.creditorName,
                          style: theme.textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          debt.reason,
                          style: theme.textTheme.bodyMedium?.copyWith(
                            color: Colors.grey[600],
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ],
                    ),
                  ),

                  // Badge de statut
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: _getStatusColor(debt.status),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(
                          debt.status.emoji,
                          style: const TextStyle(fontSize: 12),
                        ),
                        const SizedBox(width: 4),
                        Text(
                          debt.status.displayName,
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 12,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 16),

              // Montant et progression
              summaryAsync.when(
                data:
                    (summary) => Column(
                      children: [
                        // Montants
                        Row(
                          children: [
                            Expanded(
                              child: _buildAmountInfo(
                                context,
                                'Total',
                                debt.formattedTotalAmount,
                                Colors.blue,
                              ),
                            ),
                            Expanded(
                              child: _buildAmountInfo(
                                context,
                                'Payé',
                                summary.formattedTotalPaid,
                                Colors.green,
                              ),
                            ),
                            Expanded(
                              child: _buildAmountInfo(
                                context,
                                'Restant',
                                summary.formattedRemainingAmount,
                                Colors.orange,
                              ),
                            ),
                          ],
                        ),

                        const SizedBox(height: 12),

                        // Barre de progression
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Text(
                                  'Progression',
                                  style: theme.textTheme.bodySmall?.copyWith(
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                                Text(
                                  summary.formattedProgressPercentage,
                                  style: theme.textTheme.bodySmall?.copyWith(
                                    fontWeight: FontWeight.bold,
                                    color: theme.colorScheme.primary,
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 4),
                            LinearProgressIndicator(
                              value: summary.progressPercentage / 100,
                              backgroundColor: Colors.grey[300],
                              valueColor: AlwaysStoppedAnimation<Color>(
                                summary.isFullyPaid
                                    ? Colors.green
                                    : theme.colorScheme.primary,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                loading:
                    () => const Center(
                      child: Padding(
                        padding: EdgeInsets.all(16),
                        child: CircularProgressIndicator(),
                      ),
                    ),
                error:
                    (error, stack) => Text(
                      'Erreur: $error',
                      style: TextStyle(color: Colors.red[600]),
                    ),
              ),

              const SizedBox(height: 12),

              // Informations supplémentaires
              Row(
                children: [
                  // Date d'emprunt
                  Expanded(
                    child: Row(
                      children: [
                        Icon(
                          Icons.calendar_today,
                          size: 16,
                          color: Colors.grey[600],
                        ),
                        const SizedBox(width: 4),
                        Text(
                          _formatDate(debt.loanDate),
                          style: theme.textTheme.bodySmall?.copyWith(
                            color: Colors.grey[600],
                          ),
                        ),
                      ],
                    ),
                  ),

                  // Échéance
                  if (debt.dueDate != null)
                    Expanded(
                      child: Row(
                        children: [
                          Icon(
                            debt.isOverdue ? Icons.warning : Icons.schedule,
                            size: 16,
                            color:
                                debt.isOverdue ? Colors.red : Colors.grey[600],
                          ),
                          const SizedBox(width: 4),
                          Text(
                            _formatDate(debt.dueDate!),
                            style: theme.textTheme.bodySmall?.copyWith(
                              color:
                                  debt.isOverdue
                                      ? Colors.red
                                      : Colors.grey[600],
                              fontWeight:
                                  debt.isOverdue ? FontWeight.bold : null,
                            ),
                          ),
                        ],
                      ),
                    ),

                  // Type de remboursement
                  Expanded(
                    child: Row(
                      children: [
                        Icon(
                          debt.repaymentType == RepaymentType.single
                              ? Icons.payment
                              : Icons.repeat,
                          size: 16,
                          color: Colors.grey[600],
                        ),
                        const SizedBox(width: 4),
                        Text(
                          debt.repaymentType.displayName,
                          style: theme.textTheme.bodySmall?.copyWith(
                            color: Colors.grey[600],
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildAmountInfo(
    BuildContext context,
    String label,
    String amount,
    Color color,
  ) {
    final theme = Theme.of(context);

    return Column(
      children: [
        Text(
          label,
          style: theme.textTheme.bodySmall?.copyWith(
            color: Colors.grey[600],
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          amount,
          style: theme.textTheme.bodyMedium?.copyWith(
            color: color,
            fontWeight: FontWeight.bold,
          ),
        ),
      ],
    );
  }

  Color _getStatusColor(DebtStatus status) {
    switch (status) {
      case DebtStatus.active:
        return Colors.orange;
      case DebtStatus.paid:
        return Colors.green;
      case DebtStatus.overdue:
        return Colors.red;
      case DebtStatus.suspended:
        return Colors.grey;
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day.toString().padLeft(2, '0')}/'
        '${date.month.toString().padLeft(2, '0')}/'
        '${date.year}';
  }
}
