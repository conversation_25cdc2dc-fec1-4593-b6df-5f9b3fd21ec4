import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../core/providers/app_providers.dart';
import '../../../../core/utils/currency_formatter.dart';
import '../../domain/transaction.dart';

class TransactionsScreen extends ConsumerStatefulWidget {
  const TransactionsScreen({super.key});

  @override
  ConsumerState<TransactionsScreen> createState() => _TransactionsScreenState();
}

class _TransactionsScreenState extends ConsumerState<TransactionsScreen> {
  String _selectedFilter = 'Toutes';
  DateTime? _startDate;
  DateTime? _endDate;

  @override
  Widget build(BuildContext context) {
    final authService = ref.watch(authServiceProvider);
    final transactionService = ref.watch(transactionServiceProvider);
    final user = authService.currentUser;

    if (user == null) {
      return const Scaffold(
        body: Center(child: Text('Utilisateur non connecté')),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: const Text('Transactions'),
        elevation: 0,
        backgroundColor: Theme.of(context).colorScheme.primary,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.filter_list),
            onPressed: () => _showFilterDialog(context),
          ),
          IconButton(
            icon: const Icon(Icons.search),
            onPressed: () => _showSearchDialog(context),
          ),
        ],
      ),
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
              Colors.white,
            ],
          ),
        ),
        child: Column(
          children: [
            if (_selectedFilter != 'Toutes' ||
                _startDate != null ||
                _endDate != null)
              _buildActiveFilters(),
            Expanded(
              child: FutureBuilder<List<Transaction>>(
                future: Future.value(
                  transactionService.getTransactionsByUser(user.id),
                ),
                builder: (context, snapshot) {
                  if (snapshot.connectionState == ConnectionState.waiting) {
                    return const Center(child: CircularProgressIndicator());
                  }

                  if (snapshot.hasError) {
                    return Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.error_outline, // Correction de l'icône pour éviter l'erreur
                            size: 64,
                            color: Colors.grey[400],
                          ),
                          const SizedBox(height: 16),
                          Text(
                            'Erreur lors du chargement',
                            style: TextStyle(
                              fontSize: 18,
                              color: Colors.grey[600],
                            ),
                          ),
                          const SizedBox(height: 8),
                          Text(
                            snapshot.error.toString(),
                            style: TextStyle(
                              fontSize: 14,
                              color: Colors.grey[500],
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ],
                      ),
                    );
                  }

                  final transactions = _filterTransactions(snapshot.data ?? []);

                  if (transactions.isEmpty) {
                    return Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.receipt_long_outlined,
                            size: 80,
                            color: Colors.grey[400],
                          ),
                          const SizedBox(height: 16),
                          Text(
                            'Aucune transaction',
                            style: TextStyle(
                              fontSize: 20,
                              fontWeight: FontWeight.w500,
                              color: Colors.grey[600],
                            ),
                          ),
                          const SizedBox(height: 8),
                          Text(
                            'Vos transactions apparaîtront ici',
                            style: TextStyle(
                              fontSize: 14,
                              color: Colors.grey[500],
                            ),
                          ),
                        ],
                      ),
                    );
                  }

                  return RefreshIndicator(
                    onRefresh: () async {
                      setState(() {});
                    },
                    child: ListView.builder(
                      padding: const EdgeInsets.all(16),
                      itemCount: transactions.length,
                      itemBuilder: (context, index) {
                        final transaction = transactions[index];
                        return TransactionListItem(
                          transaction: transaction,
                          onTap:
                              () =>
                                  _showTransactionDetails(context, transaction),
                        );
                      },
                    ),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  List<Transaction> _filterTransactions(List<Transaction> transactions) {
    var filtered = transactions;

    // Filtrer par type
    if (_selectedFilter == 'Dépenses') {
      filtered = filtered.where((t) => t.isExpense).toList();
    } else if (_selectedFilter == 'Revenus') {
      filtered = filtered.where((t) => !t.isExpense).toList();
    }

    // Filtrer par date
    if (_startDate != null) {
      filtered =
          filtered
              .where(
                (t) => t.date.isAfter(
                  _startDate!.subtract(const Duration(days: 1)),
                ),
              )
              .toList();
    }
    if (_endDate != null) {
      filtered =
          filtered
              .where(
                (t) => t.date.isBefore(_endDate!.add(const Duration(days: 1))),
              )
              .toList();
    }

    // Trier par date (plus récent en premier)
    filtered.sort((a, b) => b.date.compareTo(a.date));

    return filtered;
  }

  Widget _buildActiveFilters() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Wrap(
        spacing: 8,
        children: [
          if (_selectedFilter != 'Toutes')
            Chip(
              label: Text(_selectedFilter),
              onDeleted: () {
                setState(() {
                  _selectedFilter = 'Toutes';
                });
              },
            ),
          if (_startDate != null)
            Chip(
              label: Text('Depuis ${_formatDate(_startDate!)}'),
              onDeleted: () {
                setState(() {
                  _startDate = null;
                });
              },
            ),
          if (_endDate != null)
            Chip(
              label: Text('Jusqu\'au ${_formatDate(_endDate!)}'),
              onDeleted: () {
                setState(() {
                  _endDate = null;
                });
              },
            ),
        ],
      ),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  void _showFilterDialog(BuildContext context) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Filtrer les transactions'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Text('Type de transaction:'),
                const SizedBox(height: 8),
                DropdownButton<String>(
                  value: _selectedFilter,
                  isExpanded: true,
                  items:
                      ['Toutes', 'Dépenses', 'Revenus']
                          .map(
                            (filter) => DropdownMenuItem(
                              value: filter,
                              child: Text(filter),
                            ),
                          )
                          .toList(),
                  onChanged: (value) {
                    setState(() {
                      _selectedFilter = value ?? 'Toutes';
                    });
                    Navigator.pop(context);
                  },
                ),
                const SizedBox(height: 16),
                Row(
                  children: [
                    Expanded(
                      child: TextButton(
                        onPressed: () async {
                          final date = await showDatePicker(
                            context: context,
                            initialDate: _startDate ?? DateTime.now(),
                            firstDate: DateTime(2020),
                            lastDate: DateTime.now(),
                          );
                          if (date != null) {
                            setState(() {
                              _startDate = date;
                            });
                          }
                        },
                        child: Text(
                          _startDate != null
                              ? 'Du ${_formatDate(_startDate!)}'
                              : 'Date de début',
                        ),
                      ),
                    ),
                    Expanded(
                      child: TextButton(
                        onPressed: () async {
                          final date = await showDatePicker(
                            context: context,
                            initialDate: _endDate ?? DateTime.now(),
                            firstDate: DateTime(2020),
                            lastDate: DateTime.now(),
                          );
                          if (date != null) {
                            setState(() {
                              _endDate = date;
                            });
                          }
                        },
                        child: Text(
                          _endDate != null
                              ? 'Au ${_formatDate(_endDate!)}'
                              : 'Date de fin',
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () {
                  setState(() {
                    _selectedFilter = 'Toutes';
                    _startDate = null;
                    _endDate = null;
                  });
                  Navigator.pop(context);
                },
                child: const Text('Réinitialiser'),
              ),
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('Fermer'),
              ),
            ],
          ),
    );
  }

  Future<void> _showSearchDialog(BuildContext context) async {
    final notificationService = ref.read(notificationServiceProvider);
    await notificationService.showNotification(
      id: DateTime.now().millisecondsSinceEpoch,
      title: 'Information',
      body: 'Fonctionnalité de recherche en cours de développement',
    );
  }

  void _showTransactionDetails(BuildContext context, Transaction transaction) {
    showDialog(
      context: context,
      builder: (context) => _TransactionDetailsDialog(transaction: transaction),
    );
  }
}

class TransactionListItem extends StatelessWidget {
  final Transaction transaction;
  final VoidCallback? onTap;

  const TransactionListItem({super.key, required this.transaction, this.onTap});

  @override
  Widget build(BuildContext context) {
    IconData getIcon() {
      if (transaction.isExpense) {
        return Icons.trending_down;
      } else {
        return Icons.trending_up;
      }
    }

    String getTimeAgo() {
      final now = DateTime.now();
      final difference = now.difference(transaction.date);

      if (difference.inDays > 0) {
        return 'Il y a ${difference.inDays} jour${difference.inDays > 1 ? 's' : ''}';
      } else if (difference.inHours > 0) {
        return 'Il y a ${difference.inHours} heure${difference.inHours > 1 ? 's' : ''}';
      } else if (difference.inMinutes > 0) {
        return 'Il y a ${difference.inMinutes} minute${difference.inMinutes > 1 ? 's' : ''}';
      } else {
        return 'À l\'instant';
      }
    }

    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              Container(
                width: 50,
                height: 50,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color:
                      transaction.isExpense
                          ? AppTheme.alertColor.withValues(alpha: 0.1)
                          : AppTheme.accentColor.withValues(alpha: 0.1),
                ),
                child: Icon(
                  getIcon(),
                  color:
                      transaction.isExpense
                          ? AppTheme.alertColor
                          : AppTheme.accentColor,
                  size: 24,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      transaction.name,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 4),
                    Text(
                      '${transaction.date.day}/${transaction.date.month}/${transaction.date.year}',
                      style: TextStyle(fontSize: 14, color: Colors.grey[600]),
                    ),
                    // Description field not available in current Transaction model
                  ],
                ),
              ),
              const SizedBox(width: 16),
              Column(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  Text(
                    '${transaction.isExpense ? '-' : '+'} ${CurrencyFormatter.format(transaction.amount)}',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color:
                          transaction.isExpense
                              ? AppTheme.alertColor
                              : AppTheme.accentColor,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    getTimeAgo(),
                    style: TextStyle(fontSize: 12, color: Colors.grey[500]),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class _TransactionDetailsDialog extends StatelessWidget {
  final Transaction transaction;

  const _TransactionDetailsDialog({required this.transaction});

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text(transaction.name),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildDetailRow('Type', transaction.isExpense ? 'Dépense' : 'Revenu'),
          _buildDetailRow(
            'Montant',
            CurrencyFormatter.format(transaction.amount),
          ),
          _buildDetailRow(
            'Date',
            '${transaction.date.day}/${transaction.date.month}/${transaction.date.year}',
          ),
          // Description field not available in current Transaction model
          _buildDetailRow(
            'Créé le',
            '${transaction.createdAt.day}/${transaction.createdAt.month}/${transaction.createdAt.year}',
          ),
          if (transaction.updatedAt != transaction.createdAt)
            _buildDetailRow(
              'Modifié le',
              '${transaction.updatedAt.day}/${transaction.updatedAt.month}/${transaction.updatedAt.year}',
            ),
        ],
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: const Text('Fermer'),
        ),
      ],
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 80,
            child: Text(
              '$label:',
              style: const TextStyle(
                fontWeight: FontWeight.w500,
                color: Colors.grey,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(fontWeight: FontWeight.w400),
            ),
          ),
        ],
      ),
    );
  }
}
