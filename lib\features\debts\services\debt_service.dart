import 'package:uuid/uuid.dart';
import '../../../core/services/storage_service.dart';
import '../domain/debt.dart';
import '../domain/repayment.dart';

class DebtService {
  final StorageService _storage;
  final Uuid _uuid = const Uuid();

  DebtService(this._storage);

  // ==================== GESTION DES DETTES ====================

  /// Créer une nouvelle dette
  Future<Debt> createDebt({
    required String userId,
    required String creditorName,
    required int totalAmount,
    required String reason,
    required DebtType type,
    required DateTime loanDate,
    DateTime? dueDate,
    required RepaymentType repaymentType,
    int? monthlyAmount,
    String? attachmentPath,
    String? notes,
  }) async {
    final debt = Debt(
      id: _uuid.v4(),
      userId: userId,
      creditorName: creditorName,
      totalAmount: totalAmount,
      reason: reason,
      type: type,
      loanDate: loanDate,
      dueDate: dueDate,
      repaymentType: repaymentType,
      monthlyAmount: monthlyAmount,
      attachmentPath: attachmentPath,
      status: DebtStatus.active,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
      notes: notes,
    );

    await _storage.saveDebt(debt);
    return debt;
  }

  /// Obtenir toutes les dettes d'un utilisateur
  List<Debt> getUserDebts(String? userId) {
    final allDebts = _storage.getAllDebts();
    if (userId == null) return allDebts;
    return allDebts.where((debt) => debt.userId == userId).toList();
  }

  /// Obtenir les dettes actives
  List<Debt> getActiveDebts(String? userId) {
    return getUserDebts(userId)
        .where((debt) => debt.status == DebtStatus.active)
        .toList();
  }

  /// Obtenir les dettes remboursées
  List<Debt> getPaidDebts(String? userId) {
    return getUserDebts(userId)
        .where((debt) => debt.status == DebtStatus.paid)
        .toList();
  }

  /// Obtenir les dettes en retard
  List<Debt> getOverdueDebts(String? userId) {
    final activeDebts = getActiveDebts(userId);
    final now = DateTime.now();
    
    return activeDebts.where((debt) {
      if (debt.dueDate == null) return false;
      return now.isAfter(debt.dueDate!);
    }).toList();
  }

  /// Obtenir une dette par ID
  Debt? getDebtById(String debtId) {
    final allDebts = _storage.getAllDebts();
    try {
      return allDebts.firstWhere((debt) => debt.id == debtId);
    } catch (e) {
      return null;
    }
  }

  /// Mettre à jour une dette
  Future<void> updateDebt(Debt debt) async {
    final updatedDebt = debt.copyWith(updatedAt: DateTime.now());
    await _storage.saveDebt(updatedDebt);
  }

  /// Supprimer une dette
  Future<void> deleteDebt(String debtId) async {
    await _storage.deleteDebt(debtId);
    
    // Supprimer aussi tous les remboursements associés
    final repayments = getRepaymentsByDebtId(debtId);
    for (final repayment in repayments) {
      await _storage.deleteRepayment(repayment.id);
    }
  }

  /// Marquer une dette comme soldée
  Future<void> markDebtAsPaid(String debtId) async {
    final debt = getDebtById(debtId);
    if (debt != null) {
      final updatedDebt = debt.copyWith(
        status: DebtStatus.paid,
        updatedAt: DateTime.now(),
      );
      await updateDebt(updatedDebt);
    }
  }

  // ==================== GESTION DES REMBOURSEMENTS ====================

  /// Ajouter un remboursement
  Future<Repayment> addRepayment({
    required String debtId,
    required int amount,
    required DateTime paymentDate,
    required PaymentMethod method,
    String? notes,
    String? receiptPath,
  }) async {
    final repayment = Repayment(
      id: _uuid.v4(),
      debtId: debtId,
      amount: amount,
      paymentDate: paymentDate,
      method: method,
      notes: notes,
      receiptPath: receiptPath,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );

    await _storage.saveRepayment(repayment);

    // Vérifier si la dette est entièrement remboursée
    await _checkAndUpdateDebtStatus(debtId);

    return repayment;
  }

  /// Obtenir tous les remboursements d'une dette
  List<Repayment> getRepaymentsByDebtId(String debtId) {
    final allRepayments = _storage.getAllRepayments();
    return allRepayments
        .where((repayment) => repayment.debtId == debtId)
        .toList()
      ..sort((a, b) => b.paymentDate.compareTo(a.paymentDate));
  }

  /// Obtenir un remboursement par ID
  Repayment? getRepaymentById(String repaymentId) {
    final allRepayments = _storage.getAllRepayments();
    try {
      return allRepayments.firstWhere((repayment) => repayment.id == repaymentId);
    } catch (e) {
      return null;
    }
  }

  /// Mettre à jour un remboursement
  Future<void> updateRepayment(Repayment repayment) async {
    final updatedRepayment = repayment.copyWith(updatedAt: DateTime.now());
    await _storage.saveRepayment(updatedRepayment);
    
    // Vérifier si la dette est entièrement remboursée
    await _checkAndUpdateDebtStatus(repayment.debtId);
  }

  /// Supprimer un remboursement
  Future<void> deleteRepayment(String repaymentId) async {
    final repayment = getRepaymentById(repaymentId);
    if (repayment != null) {
      await _storage.deleteRepayment(repaymentId);
      
      // Vérifier si la dette est entièrement remboursée
      await _checkAndUpdateDebtStatus(repayment.debtId);
    }
  }

  // ==================== CALCULS ET STATISTIQUES ====================

  /// Obtenir le résumé d'une dette
  DebtSummary getDebtSummary(String debtId) {
    final debt = getDebtById(debtId);
    if (debt == null) {
      return DebtSummary(
        totalPaid: 0,
        remainingAmount: 0,
        progressPercentage: 0,
        repayments: [],
      );
    }

    final repayments = getRepaymentsByDebtId(debtId);
    final totalPaid = repayments.fold<int>(0, (sum, repayment) => sum + repayment.amount);
    final remainingAmount = debt.totalAmount - totalPaid;
    final progressPercentage = debt.totalAmount > 0 
        ? (totalPaid / debt.totalAmount) * 100 
        : 0.0;

    return DebtSummary(
      totalPaid: totalPaid,
      remainingAmount: remainingAmount > 0 ? remainingAmount : 0,
      progressPercentage: progressPercentage > 100 ? 100 : progressPercentage,
      repayments: repayments,
    );
  }

  /// Obtenir le total des dettes actives
  int getTotalActiveDebts(String? userId) {
    final activeDebts = getActiveDebts(userId);
    return activeDebts.fold<int>(0, (sum, debt) {
      final summary = getDebtSummary(debt.id);
      return sum + summary.remainingAmount;
    });
  }

  /// Obtenir le total remboursé ce mois
  int getTotalPaidThisMonth(String? userId) {
    final now = DateTime.now();
    final startOfMonth = DateTime(now.year, now.month, 1);
    final endOfMonth = DateTime(now.year, now.month + 1, 0);

    final userDebts = getUserDebts(userId);
    int totalPaid = 0;

    for (final debt in userDebts) {
      final repayments = getRepaymentsByDebtId(debt.id);
      for (final repayment in repayments) {
        if (repayment.paymentDate.isAfter(startOfMonth) &&
            repayment.paymentDate.isBefore(endOfMonth.add(const Duration(days: 1)))) {
          totalPaid += repayment.amount;
        }
      }
    }

    return totalPaid;
  }

  /// Vérifier et mettre à jour le statut d'une dette
  Future<void> _checkAndUpdateDebtStatus(String debtId) async {
    final debt = getDebtById(debtId);
    if (debt == null) return;

    final summary = getDebtSummary(debtId);
    
    if (summary.isFullyPaid && debt.status != DebtStatus.paid) {
      await markDebtAsPaid(debtId);
    } else if (!summary.isFullyPaid && debt.status == DebtStatus.paid) {
      // Réactiver la dette si elle n'est plus entièrement payée
      final updatedDebt = debt.copyWith(
        status: DebtStatus.active,
        updatedAt: DateTime.now(),
      );
      await updateDebt(updatedDebt);
    }
  }

  /// Obtenir les dettes qui arrivent à échéance bientôt
  List<Debt> getUpcomingDueDebts(String? userId, {int daysAhead = 7}) {
    final activeDebts = getActiveDebts(userId);
    final now = DateTime.now();
    final futureDate = now.add(Duration(days: daysAhead));

    return activeDebts.where((debt) {
      if (debt.dueDate == null) return false;
      return debt.dueDate!.isAfter(now) && debt.dueDate!.isBefore(futureDate);
    }).toList();
  }
}
