import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/providers/app_providers.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../budgets/domain/goal.dart';

class GoalOverview extends ConsumerWidget {
  const GoalOverview({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final goalService = ref.watch(storageServiceProvider);
    final authService = ref.watch(authServiceProvider);

    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  children: [
                    Icon(
                      Icons.savings,
                      color: AppTheme.primaryColor,
                      size: 24,
                    ),
                    const SizedBox(width: 8),
                    const Text(
                      'Objectifs d\'épargne',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
                TextButton(
                  onPressed: () {
                    Navigator.of(context).pushNamed('/budgets');
                  },
                  child: const Text('Voir tout'),
                ),
              ],
            ),
            const SizedBox(height: 16),
            FutureBuilder<List<Goal>>(
              future: Future.value(
                goalService.getGoalsByUserId(
                  authService.currentUser?.id ?? '',
                ),
              ),
              builder: (context, snapshot) {
                if (snapshot.connectionState == ConnectionState.waiting) {
                  return const Center(child: CircularProgressIndicator());
                }
                final goals = snapshot.data ?? [];
                if (goals.isEmpty) {
                  return Center(
                    child: Column(
                      children: [
                        Icon(
                          Icons.savings_outlined,
                          size: 64,
                          color: Colors.grey[400],
                        ),
                        const SizedBox(height: 16),
                        Text(
                          'Aucun objectif d\'épargne',
                          style: TextStyle(
                            color: Colors.grey[600],
                            fontSize: 16,
                          ),
                        ),
                        const SizedBox(height: 8),
                        TextButton(
                          onPressed: () {
                            Navigator.of(context).pushNamed('/budgets');
                          },
                          child: const Text('Créer un objectif'),
                        ),
                      ],
                    ),
                  );
                }
                return Column(
                  children: goals.take(3).map((goal) {
                    final progress = goal.progress;
                    return Padding(
                      padding: const EdgeInsets.symmetric(vertical: 8),
                      child: ListTile(
                        leading: Icon(
                          goal.isCompleted ? Icons.emoji_events : Icons.savings,
                          color: goal.isCompleted ? Colors.amber : AppTheme.primaryColor,
                        ),
                        title: Text(goal.name),
                        subtitle: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            LinearProgressIndicator(
                              value: progress,
                              backgroundColor: Colors.grey[200],
                              valueColor: AlwaysStoppedAnimation<Color>(
                                progress >= 1.0 ? Colors.green : AppTheme.primaryColor,
                              ),
                            ),
                            const SizedBox(height: 4),
                            Text(
                              '${(progress * 100).toStringAsFixed(1)}% atteint',
                              style: const TextStyle(fontSize: 11, color: Colors.grey),
                            ),
                          ],
                        ),
                        trailing: Text(
                          '${(goal.currentAmount / 100).toStringAsFixed(0)} / ${(goal.targetAmount / 100).toStringAsFixed(0)} FCFA',
                          style: const TextStyle(fontWeight: FontWeight.bold),
                        ),
                      ),
                    );
                  }).toList(),
                );
              },
            ),
          ],
        ),
      ),
    );
  }
}