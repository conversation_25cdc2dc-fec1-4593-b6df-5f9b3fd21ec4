import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/providers/app_providers.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../core/widgets/page_transitions.dart';
import '../../../budgets/domain/budget.dart';
import '../../../budgets/presentation/screens/budgets_screen.dart';

class BudgetOverview extends ConsumerWidget {
  const BudgetOverview({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final budgetService = ref.watch(budgetServiceProvider);
    final authService = ref.watch(authServiceProvider);

    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  children: [
                    Icon(
                      Icons.account_balance_wallet,
                      color: AppTheme.primaryColor,
                      size: 24,
                    ),
                    const SizedBox(width: 8),
                    const Text(
                      'Aperçu des budgets',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
                TextButton(
                  onPressed: () {
                    // Navigation animée vers l'écran des budgets
                    context.pushWithAnimation(
                      const BudgetsScreen(),
                      type: PageTransitionType.slideRight,
                    );
                  },
                  child: const Text('Voir tout'),
                ),
              ],
            ),
            const SizedBox(height: 16),
            FutureBuilder<List<Budget>>(
              future: Future.value(
                budgetService.getActiveBudgets(
                  authService.currentUser?.id ?? '',
                ),
              ),
              builder: (context, snapshot) {
                if (snapshot.connectionState == ConnectionState.waiting) {
                  return const Center(child: CircularProgressIndicator());
                }

                final budgets = snapshot.data ?? [];

                if (budgets.isEmpty) {
                  return Center(
                    child: Column(
                      children: [
                        Icon(
                          Icons.account_balance_wallet_outlined,
                          size: 64,
                          color: Colors.grey[400],
                        ),
                        const SizedBox(height: 16),
                        Text(
                          'Aucun budget actif',
                          style: TextStyle(
                            color: Colors.grey[600],
                            fontSize: 16,
                          ),
                        ),
                        const SizedBox(height: 8),
                        TextButton(
                          onPressed: () {
                            context.pushWithAnimation(
                              const BudgetsScreen(),
                              type: PageTransitionType.fade,
                            );
                          },
                          child: const Text('Créer un budget'),
                        ),
                      ],
                    ),
                  );
                }

                // Afficher les 3 premiers budgets
                final displayBudgets = budgets.take(3).toList();

                return Column(
                  children:
                      displayBudgets.map((budget) {
                        return Padding(
                          padding: const EdgeInsets.only(bottom: 12),
                          child: _BudgetItem(budget: budget),
                        );
                      }).toList(),
                );
              },
            ),
          ],
        ),
      ),
    );
  }
}

class _BudgetItem extends StatelessWidget {
  final Budget budget;

  const _BudgetItem({required this.budget});

  @override
  Widget build(BuildContext context) {
    final usagePercentage = budget.usagePercentage;
    final remainingAmount = budget.remainingAmount;
    final status = budget.status;

    Color progressColor;
    Color statusColor;
    IconData statusIcon;

    switch (status) {
      case BudgetStatus.onTrack:
        progressColor = Colors.green;
        statusColor = Colors.green;
        statusIcon = Icons.check_circle;
        break;
      case BudgetStatus.warning:
        progressColor = Colors.orange;
        statusColor = Colors.orange;
        statusIcon = Icons.warning;
        break;
      case BudgetStatus.exceeded:
        progressColor = Colors.red;
        statusColor = Colors.red;
        statusIcon =
            Icons.error_outline; // Correction de l'icône pour éviter l'erreur
        break;
    }

    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[200]!),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                child: Text(
                  budget.name,
                  style: const TextStyle(
                    fontWeight: FontWeight.w600,
                    fontSize: 14,
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              Row(
                children: [
                  Icon(statusIcon, color: statusColor, size: 16),
                  const SizedBox(width: 4),
                  Text(
                    status.displayName,
                    style: TextStyle(
                      color: statusColor,
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ],
          ),
          const SizedBox(height: 8),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                budget.formattedSpentAmount,
                style: const TextStyle(fontSize: 12, color: Colors.grey),
              ),
              Text(
                budget.formattedAllocatedAmount,
                style: const TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
          const SizedBox(height: 4),
          LinearProgressIndicator(
            value: usagePercentage / 100,
            backgroundColor: Colors.grey[300],
            valueColor: AlwaysStoppedAnimation<Color>(progressColor),
            minHeight: 6,
          ),
          const SizedBox(height: 4),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                '${usagePercentage.toStringAsFixed(1)}% utilisé',
                style: const TextStyle(fontSize: 11, color: Colors.grey),
              ),
              Text(
                'Reste: ${remainingAmount.toStringAsFixed(0)} FCFA',
                style: TextStyle(
                  fontSize: 11,
                  color: remainingAmount >= 0 ? Colors.green : Colors.red,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
