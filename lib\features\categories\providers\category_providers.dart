import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../domain/category.dart';
import '../domain/subcategory.dart';
import '../services/category_service.dart';
import '../../../core/providers/app_providers.dart';

// Provider pour obtenir toutes les catégories
final categoriesProvider = FutureProvider<List<Category>>((ref) async {
  final categoryService = ref.watch(categoryServiceProvider);
  return categoryService.getAllCategories();
});

// Provider pour obtenir les catégories par type
final categoriesByTypeProvider = FutureProvider.family<List<Category>, CategoryType>((ref, type) async {
  final categoryService = ref.watch(categoryServiceProvider);
  return categoryService.getCategoriesByType(type);
});

// Provider pour obtenir les sous-catégories d'une catégorie
final subCategoriesProvider = FutureProvider.family<List<SubCategory>, String>((ref, categoryId) async {
  final categoryService = ref.watch(categoryServiceProvider);
  return categoryService.getSubCategoriesByCategory(categoryId);
});

// Provider pour obtenir une catégorie par ID
final categoryByIdProvider = FutureProvider.family<Category?, String>((ref, categoryId) async {
  final categoryService = ref.watch(categoryServiceProvider);
  return categoryService.getCategoryById(categoryId);
});

// Provider pour obtenir une sous-catégorie par ID
final subCategoryByIdProvider = FutureProvider.family<SubCategory?, String>((ref, subCategoryId) async {
  final categoryService = ref.watch(categoryServiceProvider);
  return categoryService.getSubCategoryById(subCategoryId);
});

// Provider pour les catégories de dépenses (pour compatibilité avec l'ancien système)
final expenseCategoriesProvider = FutureProvider<List<String>>((ref) async {
  final categoryService = ref.watch(categoryServiceProvider);
  final categories = await categoryService.getCategoriesByType(CategoryType.expense);
  return categories.map((c) => c.name).toList();
});

// Provider pour les catégories de revenus (pour compatibilité avec l'ancien système)
final incomeCategoriesProvider = FutureProvider<List<String>>((ref) async {
  final categoryService = ref.watch(categoryServiceProvider);
  final categories = await categoryService.getCategoriesByType(CategoryType.income);
  return categories.map((c) => c.name).toList();
});

// Provider pour toutes les catégories sous forme de noms (pour compatibilité)
final allCategoryNamesProvider = FutureProvider<List<String>>((ref) async {
  final categoryService = ref.watch(categoryServiceProvider);
  final categories = await categoryService.getAllCategories();
  return categories.map((c) => c.name).toList();
});