import 'package:hive/hive.dart';

part 'subcategory.g.dart';

@HiveType(typeId: 5)
class SubCategory {
  @HiveField(0)
  final String id;
  
  @HiveField(1)
  final String name;
  
  @HiveField(2)
  final String categoryId;
  
  @HiveField(3)
  final String? userId; // null pour les sous-catégories prédéfinies
  
  @HiveField(4)
  final String? iconName;
  
  @HiveField(5)
  final bool isActive;
  
  @HiveField(6)
  final DateTime createdAt;
  
  @HiveField(7)
  final DateTime updatedAt;

  SubCategory({
    required this.id,
    required this.name,
    required this.categoryId,
    this.userId,
    this.iconName,
    this.isActive = true,
    required this.createdAt,
    required this.updatedAt,
  });

  SubCategory copyWith({
    String? id,
    String? name,
    String? categoryId,
    String? userId,
    String? iconName,
    bool? isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return SubCategory(
      id: id ?? this.id,
      name: name ?? this.name,
      categoryId: categoryId ?? this.categoryId,
      userId: userId ?? this.userId,
      iconName: iconName ?? this.iconName,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'categoryId': categoryId,
      'userId': userId,
      'iconName': iconName,
      'isActive': isActive,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }

  factory SubCategory.fromJson(Map<String, dynamic> json) {
    return SubCategory(
      id: json['id'],
      name: json['name'],
      categoryId: json['categoryId'],
      userId: json['userId'],
      iconName: json['iconName'],
      isActive: json['isActive'] ?? true,
      createdAt: DateTime.parse(json['createdAt']),
      updatedAt: DateTime.parse(json['updatedAt']),
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is SubCategory && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'SubCategory(id: $id, name: $name, categoryId: $categoryId, isActive: $isActive)';
  }
}