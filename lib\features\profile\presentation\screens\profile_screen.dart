import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:form_validator/form_validator.dart';
import 'package:image_picker/image_picker.dart';
import 'dart:io';
import '../../../../core/theme/app_theme.dart';
import '../../../../core/providers/app_providers.dart';
import '../../../../core/widgets/animated_popup.dart';
import '../../../../core/widgets/page_transitions.dart';
import '../../../auth/presentation/screens/login_screen.dart';
import '../widgets/theme_selector.dart';

class ProfileScreen extends ConsumerStatefulWidget {
  const ProfileScreen({super.key});

  @override
  ConsumerState<ProfileScreen> createState() => _ProfileScreenState();
}

class _ProfileScreenState extends ConsumerState<ProfileScreen> {
  bool _notificationsEnabled = true;
  String _selectedLanguage = 'Français';

  @override
  Widget build(BuildContext context) {
    final authService = ref.watch(authServiceProvider);
    final user = authService.currentUser;

    return Scaffold(
      appBar: AppBar(
        title: const Text('Profil'),
        elevation: 0,
        backgroundColor: Theme.of(context).colorScheme.primary,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.edit),
            onPressed: () {
              _showEditProfileDialog(context);
            },
          ),
        ],
      ),
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
              Colors.white,
            ],
          ),
        ),
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              const SizedBox(height: 20),
              Container(
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.1),
                      blurRadius: 10,
                      offset: const Offset(0, 5),
                    ),
                  ],
                ),
                child: CircleAvatar(
                  radius: 60,
                  backgroundColor: Theme.of(context).colorScheme.primary,
                  backgroundImage: user?.profilePicture != null
                      ? FileImage(File(user!.profilePicture!))
                      : null,
                  child: user?.profilePicture == null
                      ? Text(
                          user?.firstName.isNotEmpty == true
                              ? '${user!.firstName[0]}${user.lastName.isNotEmpty ? user.lastName[0] : ''}'
                              : 'U',
                          style: const TextStyle(
                            fontSize: 32,
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                          ),
                        )
                      : null,
                ),
              ),
              const SizedBox(height: 20),
              Text(
                '${user?.firstName ?? ''} ${user?.lastName ?? ''}',
                style: const TextStyle(
                  fontSize: 28,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                user?.email ?? 'Email non disponible',
                style: TextStyle(fontSize: 16, color: Colors.grey[600]),
              ),
              const SizedBox(height: 8),
              Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 6,
                ),
                decoration: BoxDecoration(
                  color: Theme.of(
                    context,
                  ).colorScheme.primary.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Text(
                  'Membre depuis ${_formatDate(user?.createdAt)}',
                  style: TextStyle(
                    fontSize: 12,
                    color: Theme.of(context).colorScheme.primary,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
              const SizedBox(height: 32),
              _buildSettingsSection(context),
              const SizedBox(height: 16),
              _buildAccountSection(context),
              const SizedBox(height: 32),
              SizedBox(
                width: double.infinity,
                child: ElevatedButton.icon(
                  onPressed: () => _showLogoutConfirmation(context),
                  icon: const Icon(Icons.logout),
                  label: const Text('Déconnexion'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppTheme.alertColor,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(
                      horizontal: 32,
                      vertical: 16,
                    ),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                ),
              ),
              const SizedBox(height: 20),
            ],
          ),
        ),
      ),
    );
  }

  String _formatDate(DateTime? date) {
    if (date == null) return 'récemment';
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays < 30) {
      return '${difference.inDays} jours';
    } else if (difference.inDays < 365) {
      return '${(difference.inDays / 30).floor()} mois';
    } else {
      return '${(difference.inDays / 365).floor()} ans';
    }
  }

  Widget _buildSettingsSection(BuildContext context) {
    final isDarkMode = ref.watch(themeModeProvider);
    final currentThemeType = ref.watch(themeTypeProvider);

    return Card(
      child: Column(
        children: [
          const ListTile(
            title: Text(
              'Paramètres',
              style: TextStyle(fontWeight: FontWeight.bold, fontSize: 18),
            ),
          ),
          SwitchListTile(
            title: const Text('Notifications'),
            subtitle: const Text('Activer les notifications push'),
            value: _notificationsEnabled,
            onChanged: (value) {
              setState(() {
                _notificationsEnabled = value;
              });
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(
                    value
                        ? 'Notifications activées'
                        : 'Notifications désactivées',
                  ),
                  duration: const Duration(seconds: 2),
                ),
              );
            },
          ),
          ListTile(
            title: const Text('Langue'),
            subtitle: Text(_selectedLanguage),
            trailing: const Icon(Icons.arrow_forward_ios, size: 16),
            onTap: () {
              _showLanguageSelectionDialog(context);
            },
          ),
          SwitchListTile(
            title: const Text('Mode sombre'),
            subtitle: const Text('Changer l\'apparence de l\'application'),
            value: isDarkMode,
            onChanged: (value) {
              ref.read(themeModeProvider.notifier).toggleTheme();
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(
                    value ? 'Mode sombre activé' : 'Mode clair activé',
                  ),
                  duration: const Duration(seconds: 2),
                ),
              );
            },
          ),
          ListTile(
            leading: const Icon(Icons.palette),
            title: const Text('Thème'),
            subtitle: Text(
              'Thème actuel: ${AppTheme.themeNames[currentThemeType]}',
            ),
            trailing: const Icon(Icons.arrow_forward_ios, size: 16),
            onTap: () {
              _showThemeSelector(context);
            },
          ),
          ListTile(
            leading: const Icon(Icons.photo_camera),
            title: const Text('Photo de profil'),
            subtitle: const Text('Modifier votre photo de profil'),
            trailing: const Icon(Icons.arrow_forward_ios, size: 16),
            onTap: () {
              _showProfilePictureDialog(context);
            },
          ),
          ListTile(
            leading: const Icon(Icons.security),
            title: const Text('Code d\'accès'),
            subtitle: const Text('Modifier le code d\'accès rapide'),
            trailing: const Icon(Icons.arrow_forward_ios, size: 16),
            onTap: () {
              _showAccessCodeDialog(context);
            },
          ),
          ListTile(
            leading: const Icon(Icons.image),
            title: const Text('Écran de démarrage'),
            subtitle: const Text('Personnaliser l\'écran de démarrage'),
            trailing: const Icon(Icons.arrow_forward_ios, size: 16),
            onTap: () {
              _showSplashScreenDialog(context);
            },
          ),
        ],
      ),
    );
  }

  Widget _buildAccountSection(BuildContext context) {
    return Card(
      child: Column(
        children: [
          const ListTile(
            title: Text(
              'Compte',
              style: TextStyle(fontWeight: FontWeight.bold, fontSize: 18),
            ),
          ),
          ListTile(
            leading: const Icon(Icons.lock),
            title: const Text('Changer le mot de passe'),
            trailing: const Icon(Icons.arrow_forward_ios, size: 16),
            onTap: () {
              _showChangePasswordDialog(context);
            },
          ),
          ListTile(
            leading: const Icon(Icons.account_balance_wallet),
            title: const Text('Comptes bancaires'),
            trailing: const Icon(Icons.arrow_forward_ios, size: 16),
            onTap: () {
              _showBankAccountsDialog(context);
            },
          ),
          ListTile(
            leading: const Icon(Icons.help),
            title: const Text('Aide et support'),
            trailing: const Icon(Icons.arrow_forward_ios, size: 16),
            onTap: () {
              _showHelpDialog(context);
            },
          ),
        ],
      ),
    );
  }

  void _showEditProfileDialog(BuildContext context) {
    showDialog(context: context, builder: (context) => _EditProfileDialog());
  }

  Future<void> _showLogoutConfirmation(BuildContext context) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Déconnexion'),
            content: const Text('Êtes-vous sûr de vouloir vous déconnecter ?'),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(false),
                child: const Text('Annuler'),
              ),
              ElevatedButton(
                onPressed: () => Navigator.of(context).pop(true),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppTheme.alertColor,
                  foregroundColor: Colors.white,
                ),
                child: const Text('Déconnexion'),
              ),
            ],
          ),
    );

    if (confirmed == true) {
      final authService = ref.read(authServiceProvider);
      await authService.logout();

      if (mounted) {
        // ignore: use_build_context_synchronously
        context.pushWithAnimation(
          const LoginScreen(),
          type: PageTransitionType.slideLeft,
        );
      }
    }
  }

  void _showLanguageSelectionDialog(BuildContext context) {
    final languages = ['Français', 'Anglais', 'Espagnol'];
    AnimatedPopup.show(
      context: context,
      animationType: PopupAnimationType.scale,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children:
            languages.map((language) {
              return ListTile(
                title: Text(language),
                leading: Radio<String>(
                  value: language,
                  groupValue: _selectedLanguage,
                  onChanged: (value) {
                    setState(() {
                      _selectedLanguage = value!;
                    });
                    Navigator.pop(context);
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text('Langue mise à jour: $value'),
                        duration: const Duration(seconds: 2),
                      ),
                    );
                  },
                ),
              );
            }).toList(),
      ),
    );
  }

  void _showChangePasswordDialog(BuildContext context) {
    AnimatedPopup.show(
      context: context,
      animationType: PopupAnimationType.slideFromBottom,
      child: _ChangePasswordDialog(),
    );
  }

  Future<void> _showBankAccountsDialog(BuildContext context) async {
    final notificationService = ref.read(notificationServiceProvider);
    await notificationService.showNotification(
      id: DateTime.now().millisecondsSinceEpoch,
      title: 'Information',
      body: 'Fonctionnalité en cours de développement',
    );
  }

  void _showHelpDialog(BuildContext context) {
    AnimatedPopup.show(
      context: context,
      animationType: PopupAnimationType.fade,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Besoin d\'aide ?',
            style: TextStyle(fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 8),
          const Text('• Consultez notre FAQ en ligne'),
          const Text('• Contactez-nous par email: <EMAIL>'),
          const Text('• Appelez-nous au: +225 XX XX XX XX'),
          const SizedBox(height: 16),
          const Text(
            'Version de l\'application: 1.0.0',
            style: TextStyle(fontSize: 12, color: Colors.grey),
          ),
          const SizedBox(height: 16),
          Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('Fermer'),
              ),
            ],
          ),
        ],
      ),
    );
  }

  void _showThemeSelector(BuildContext context) {
    AnimatedPopup.show(
      context: context,
      animationType: PopupAnimationType.slideFromBottom,
      child: AnimatedBottomSheet(
        child: Material(
          color: Colors.transparent,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Text(
                'Sélectionner un thème',
                style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 20),
              const ThemeSelector(),
              const SizedBox(height: 16),
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('Fermer'),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class _EditProfileDialog extends ConsumerStatefulWidget {
  @override
  ConsumerState<_EditProfileDialog> createState() => _EditProfileDialogState();
}

class _EditProfileDialogState extends ConsumerState<_EditProfileDialog> {
  final _formKey = GlobalKey<FormState>();
  late final TextEditingController _firstNameController;
  late final TextEditingController _lastNameController;
  late final TextEditingController _emailController;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    final user = ref.read(authServiceProvider).currentUser;
    _firstNameController = TextEditingController(text: user?.firstName ?? '');
    _lastNameController = TextEditingController(text: user?.lastName ?? '');
    _emailController = TextEditingController(text: user?.email ?? '');
  }

  @override
  void dispose() {
    _firstNameController.dispose();
    _lastNameController.dispose();
    _emailController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Modifier le profil'),
      content: Form(
        key: _formKey,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextFormField(
              controller: _firstNameController,
              decoration: const InputDecoration(
                labelText: 'Prénom',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.person),
              ),
              validator:
                  ValidationBuilder().required('Le prénom est requis').build(),
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _lastNameController,
              decoration: const InputDecoration(
                labelText: 'Nom',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.person_outline),
              ),
              validator:
                  ValidationBuilder().required('Le nom est requis').build(),
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _emailController,
              decoration: const InputDecoration(
                labelText: 'Email',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.email),
              ),
              keyboardType: TextInputType.emailAddress,
              validator:
                  ValidationBuilder()
                      .required('L\'email est requis')
                      .email('Email invalide')
                      .build(),
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: _isLoading ? null : () => Navigator.pop(context),
          child: const Text('Annuler'),
        ),
        ElevatedButton(
          onPressed: _isLoading ? null : _updateProfile,
          child:
              _isLoading
                  ? const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  )
                  : const Text('Enregistrer'),
        ),
      ],
    );
  }

  Future<void> _updateProfile() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final authService = ref.read(authServiceProvider);
      final notificationService = ref.read(notificationServiceProvider);
      final currentUser = authService.currentUser;

      if (currentUser != null) {
        final updatedUser = currentUser.copyWith(
          firstName: _firstNameController.text.trim(),
          lastName: _lastNameController.text.trim(),
          email: _emailController.text.trim(),
          updatedAt: DateTime.now(),
        );

        final result = await authService.updateProfile(
          firstName: updatedUser.firstName,
          lastName: updatedUser.lastName,
          profilePicture: updatedUser.profilePicture,
        );

        if (!result.success) {
          throw Exception(result.message ?? 'Erreur de mise à jour');
        }
        await notificationService.showNotification(
          id: DateTime.now().millisecondsSinceEpoch,
          title: 'Succès',
          body: 'Profil mis à jour avec succès',
        );

        if (mounted) {
          Navigator.pop(context);
        }
      }
    } catch (e) {
      final notificationService = ref.read(notificationServiceProvider);
      await notificationService.showNotification(
        id: DateTime.now().millisecondsSinceEpoch,
        title: 'Erreur',
        body: 'Erreur lors de la mise à jour du profil',
      );
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  void _showProfilePictureDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Photo de profil'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.camera_alt),
              title: const Text('Prendre une photo'),
              onTap: () {
                Navigator.pop(context);
                _pickImage(ImageSource.camera);
              },
            ),
            ListTile(
              leading: const Icon(Icons.photo_library),
              title: const Text('Choisir depuis la galerie'),
              onTap: () {
                Navigator.pop(context);
                _pickImage(ImageSource.gallery);
              },
            ),
            ListTile(
              leading: const Icon(Icons.delete),
              title: const Text('Supprimer la photo'),
              onTap: () {
                Navigator.pop(context);
                _removeProfilePicture();
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Annuler'),
          ),
        ],
      ),
    );
  }

  Future<void> _pickImage(ImageSource source) async {
    try {
      final ImagePicker picker = ImagePicker();
      final XFile? image = await picker.pickImage(source: source);
      
      if (image != null) {
        final authService = ref.read(authServiceProvider);
        await authService.updateProfilePicture(image.path);
        
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Photo de profil mise à jour'),
              duration: Duration(seconds: 2),
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Erreur lors de la mise à jour de la photo'),
            duration: Duration(seconds: 2),
          ),
        );
      }
    }
  }

  Future<void> _removeProfilePicture() async {
    try {
      final authService = ref.read(authServiceProvider);
      await authService.updateProfilePicture(null);
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Photo de profil supprimée'),
            duration: Duration(seconds: 2),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Erreur lors de la suppression de la photo'),
            duration: Duration(seconds: 2),
          ),
        );
      }
    }
  }

  void _showAccessCodeDialog(BuildContext context) {
    final TextEditingController codeController = TextEditingController();
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Code d\'accès'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text('Entrez le nouveau code d\'accès (8 chiffres minimum):'),
            const SizedBox(height: 16),
            TextField(
              controller: codeController,
              keyboardType: TextInputType.number,
              maxLength: 12,
              decoration: const InputDecoration(
                labelText: 'Nouveau code',
                border: OutlineInputBorder(),
                counterText: '',
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Annuler'),
          ),
          ElevatedButton(
            onPressed: () {
              if (codeController.text.length >= 8) {
                _updateAccessCode(codeController.text);
                Navigator.pop(context);
              } else {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Le code doit contenir au moins 8 chiffres'),
                  ),
                );
              }
            },
            child: const Text('Modifier'),
          ),
        ],
      ),
    );
  }

  Future<void> _updateAccessCode(String newCode) async {
    try {
      final authService = ref.read(authServiceProvider);
      await authService.updateAccessCode(newCode);
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Code d\'accès mis à jour'),
            duration: Duration(seconds: 2),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Erreur lors de la mise à jour du code'),
            duration: Duration(seconds: 2),
          ),
        );
      }
    }
  }

  void _showSplashScreenDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => _SplashScreenSettingsDialog(),
    );
  }
}

class _SplashScreenSettingsDialog extends ConsumerStatefulWidget {
  @override
  ConsumerState<_SplashScreenSettingsDialog> createState() => _SplashScreenSettingsDialogState();
}

class _SplashScreenSettingsDialogState extends ConsumerState<_SplashScreenSettingsDialog> {
  bool _splashEnabled = true;
  String? _splashImagePath;

  @override
  void initState() {
    super.initState();
    _loadSplashSettings();
  }

  Future<void> _loadSplashSettings() async {
    final storageService = ref.read(storageServiceProvider);
    final enabled = await storageService.getSplashEnabled();
    final imagePath = await storageService.getSplashImagePath();
    
    setState(() {
      _splashEnabled = enabled;
      _splashImagePath = imagePath;
    });
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Écran de démarrage'),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          SwitchListTile(
            title: const Text('Activer l\'écran de démarrage'),
            value: _splashEnabled,
            onChanged: (value) {
              setState(() {
                _splashEnabled = value;
              });
              _saveSplashEnabled(value);
            },
          ),
          const SizedBox(height: 16),
          if (_splashImagePath != null)
            Container(
              height: 100,
              width: 100,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(8),
                image: DecorationImage(
                  image: FileImage(File(_splashImagePath!)),
                  fit: BoxFit.cover,
                ),
              ),
            ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: _pickSplashImage,
                  icon: const Icon(Icons.image),
                  label: Text(_splashImagePath == null ? 'Choisir une image' : 'Changer l\'image'),
                ),
              ),
              if (_splashImagePath != null) ...[
                const SizedBox(width: 8),
                IconButton(
                  onPressed: _removeSplashImage,
                  icon: const Icon(Icons.delete),
                  color: Colors.red,
                ),
              ],
            ],
          ),
        ],
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: const Text('Fermer'),
        ),
      ],
    );
  }

  Future<void> _pickSplashImage() async {
    try {
      final ImagePicker picker = ImagePicker();
      final XFile? image = await picker.pickImage(source: ImageSource.gallery);
      
      if (image != null) {
        final storageService = ref.read(storageServiceProvider);
        await storageService.saveSplashImagePath(image.path);
        
        setState(() {
          _splashImagePath = image.path;
        });
        
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Image de démarrage mise à jour'),
              duration: Duration(seconds: 2),
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Erreur lors de la sélection de l\'image'),
            duration: Duration(seconds: 2),
          ),
        );
      }
    }
  }

  Future<void> _removeSplashImage() async {
    try {
      final storageService = ref.read(storageServiceProvider);
      await storageService.saveSplashImagePath(null);
      
      setState(() {
        _splashImagePath = null;
      });
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Image de démarrage supprimée'),
            duration: Duration(seconds: 2),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Erreur lors de la suppression de l\'image'),
            duration: Duration(seconds: 2),
          ),
        );
      }
    }
  }

  Future<void> _saveSplashEnabled(bool enabled) async {
    try {
      final storageService = ref.read(storageServiceProvider);
      await storageService.saveSplashEnabled(enabled);
    } catch (e) {
      // Handle error silently
    }
  }
}

class _ChangePasswordDialog extends ConsumerStatefulWidget {
  @override
  ConsumerState<_ChangePasswordDialog> createState() =>
      _ChangePasswordDialogState();
}

class _ChangePasswordDialogState extends ConsumerState<_ChangePasswordDialog> {
  final _formKey = GlobalKey<FormState>();
  final _oldPasswordController = TextEditingController();
  final _newPasswordController = TextEditingController();
  final _confirmPasswordController = TextEditingController();
  bool _isLoading = false;
  bool _showOldPassword = false;
  bool _showNewPassword = false;
  bool _showConfirmPassword = false;

  @override
  void dispose() {
    _oldPasswordController.dispose();
    _newPasswordController.dispose();
    _confirmPasswordController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Changer le mot de passe'),
      content: Form(
        key: _formKey,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextFormField(
              controller: _oldPasswordController,
              decoration: InputDecoration(
                labelText: 'Ancien mot de passe',
                border: const OutlineInputBorder(),
                prefixIcon: const Icon(Icons.lock_outline),
                suffixIcon: IconButton(
                  icon: Icon(
                    _showOldPassword ? Icons.visibility_off : Icons.visibility,
                  ),
                  onPressed: () {
                    setState(() {
                      _showOldPassword = !_showOldPassword;
                    });
                  },
                ),
              ),
              obscureText: !_showOldPassword,
              validator:
                  ValidationBuilder()
                      .required('L\'ancien mot de passe est requis')
                      .build(),
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _newPasswordController,
              decoration: InputDecoration(
                labelText: 'Nouveau mot de passe',
                border: const OutlineInputBorder(),
                prefixIcon: const Icon(Icons.lock),
                suffixIcon: IconButton(
                  icon: Icon(
                    _showNewPassword ? Icons.visibility_off : Icons.visibility,
                  ),
                  onPressed: () {
                    setState(() {
                      _showNewPassword = !_showNewPassword;
                    });
                  },
                ),
              ),
              obscureText: !_showNewPassword,
              validator:
                  ValidationBuilder()
                      .required('Le nouveau mot de passe est requis')
                      .minLength(
                        6,
                        'Le mot de passe doit contenir au moins 6 caractères',
                      )
                      .build(),
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _confirmPasswordController,
              decoration: InputDecoration(
                labelText: 'Confirmer le nouveau mot de passe',
                border: const OutlineInputBorder(),
                prefixIcon: const Icon(Icons.lock_clock),
                suffixIcon: IconButton(
                  icon: Icon(
                    _showConfirmPassword
                        ? Icons.visibility_off
                        : Icons.visibility,
                  ),
                  onPressed: () {
                    setState(() {
                      _showConfirmPassword = !_showConfirmPassword;
                    });
                  },
                ),
              ),
              obscureText: !_showConfirmPassword,
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Veuillez confirmer le mot de passe';
                }
                if (value != _newPasswordController.text) {
                  return 'Les mots de passe ne correspondent pas';
                }
                return null;
              },
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: _isLoading ? null : () => Navigator.pop(context),
          child: const Text('Annuler'),
        ),
        ElevatedButton(
          onPressed: _isLoading ? null : _changePassword,
          child:
              _isLoading
                  ? const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  )
                  : const Text('Changer'),
        ),
      ],
    );
  }

  Future<void> _changePassword() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final authService = ref.read(authServiceProvider);
      final notificationService = ref.read(notificationServiceProvider);

      final result = await authService.changePassword(
        _oldPasswordController.text,
        _newPasswordController.text,
      );

      if (!result.success) {
        throw Exception(
          result.message ?? 'Erreur de changement de mot de passe',
        );
      }

      await notificationService.showNotification(
        id: DateTime.now().millisecondsSinceEpoch,
        title: 'Succès',
        body: 'Mot de passe mis à jour avec succès',
      );

      if (mounted) {
        Navigator.pop(context);
      }
    } catch (e) {
      final notificationService = ref.read(notificationServiceProvider);
      await notificationService.showNotification(
        id: DateTime.now().millisecondsSinceEpoch,
        title: 'Erreur',
        body: 'Erreur lors du changement de mot de passe',
      );
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }
}
