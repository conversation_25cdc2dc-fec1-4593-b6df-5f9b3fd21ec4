import 'package:intl/intl.dart';

class CurrencyFormatter {
  static String format(int amountInCents) {
    final formatter = NumberFormat.currency(
      locale: 'fr_FR',
      symbol: 'FCFA',
      decimalDigits: 0,
    );

    // Convertir les centimes en unités
    final amount = amountInCents / 100;

    return formatter.format(amount);
  }

  static String formatAmount(double amount) {
    final formatter = NumberFormat.currency(
      locale: 'fr_FR',
      symbol: 'FCFA',
      decimalDigits: 0,
    );

    return formatter.format(amount);
  }

  static String formatCompact(double amount) {
    if (amount >= 1000000) {
      return '${(amount / 1000000).toStringAsFixed(1)}M FCFA';
    } else if (amount >= 1000) {
      return '${(amount / 1000).toStringAsFixed(1)}K FCFA';
    } else {
      return '${amount.toStringAsFixed(0)} FCFA';
    }
  }

  // Formatage pour le dashboard principal - seuils plus élevés
  static String formatDashboard(double amount) {
    if (amount >= 10000000) { // 10 millions et plus
      return '${(amount / 1000000).toStringAsFixed(1)}M FCFA';
    } else if (amount >= 100000) { // 100K et plus
      return '${(amount / 1000).toStringAsFixed(0)}K FCFA';
    } else {
      return '${amount.toStringAsFixed(0)} FCFA';
    }
  }

  // Formatage pour les listes de transactions - pas de compactage
  static String formatTransaction(double amount) {
    return formatAmount(amount);
  }

  // Formatage pour les budgets - seuils modérés
  static String formatBudget(double amount) {
    if (amount >= 5000000) { // 5 millions et plus
      return '${(amount / 1000000).toStringAsFixed(1)}M FCFA';
    } else if (amount >= 50000) { // 50K et plus
      return '${(amount / 1000).toStringAsFixed(0)}K FCFA';
    } else {
      return '${amount.toStringAsFixed(0)} FCFA';
    }
  }
}
