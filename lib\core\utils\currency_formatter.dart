import 'package:intl/intl.dart';

class CurrencyFormatter {
  static String format(int amountInCents) {
    final formatter = NumberFormat.currency(
      locale: 'fr_FR',
      symbol: 'FCFA',
      decimalDigits: 0,
    );

    // Convertir les centimes en unités
    final amount = amountInCents / 100;

    return formatter.format(amount);
  }

  static String formatAmount(double amount) {
    final formatter = NumberFormat.currency(
      locale: 'fr_FR',
      symbol: 'FCFA',
      decimalDigits: 0,
    );

    return formatter.format(amount);
  }

  static String formatCompact(double amount) {
    if (amount >= 1000000) {
      return '${(amount / 1000000).toStringAsFixed(1)}M FCFA';
    } else if (amount >= 1000) {
      return '${(amount / 1000).toStringAsFixed(1)}K FCFA';
    } else {
      return '${amount.toStringAsFixed(0)} FCFA';
    }
  }
}
