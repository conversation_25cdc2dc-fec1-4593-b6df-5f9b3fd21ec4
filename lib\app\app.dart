import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../core/theme/app_theme.dart';
import '../core/providers/app_providers.dart';
import '../features/auth/presentation/screens/login_screen.dart';
import '../features/dashboard/presentation/screens/dashboard_screen.dart';
import '../features/transactions/presentation/screens/transactions_screen.dart';
import '../features/budgets/presentation/screens/budgets_screen.dart';
import '../features/categories/presentation/screens/categories_screen.dart';
import '../features/profile/presentation/screens/profile_screen.dart';
import '../features/splash/presentation/screens/splash_screen.dart';

class ZoroBudgetApp extends ConsumerStatefulWidget {
  const ZoroBudgetApp({super.key});

  @override
  ZoroBudgetAppState createState() => ZoroBudgetAppState();
}

class ZoroBudgetAppState extends ConsumerState<ZoroBudgetApp> {
  @override
  void initState() {
    super.initState();
    // Initialiser les préférences après le premier build
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _initializeSettings();
    });
  }

  Future<void> _initializeSettings() async {
    // Charger le thème une fois que l'application est initialisée
    final themeNotifier = ref.read(themeModeProvider.notifier);
    await themeNotifier.loadThemeMode();

    // Charger la langue (à implémenter plus tard si nécessaire)
    // final languageNotifier = ref.read(languageProvider.notifier);
    // await languageNotifier.loadLanguage();
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = ref.watch(themeModeProvider);
    final themeType = ref.watch(themeTypeProvider);
    final language = ref.watch(languageProvider);

    return MaterialApp(
      title: 'Zoro Budget Familial',
      theme: AppTheme.getLightTheme(themeType),
      darkTheme: AppTheme.getDarkTheme(themeType),
      themeMode: isDarkMode ? ThemeMode.dark : ThemeMode.light,
      locale: Locale(language),
      home: const SplashWrapper(),
      routes: {
        '/login': (context) => const LoginScreen(),
        '/dashboard': (context) => const DashboardScreen(),
        '/transactions': (context) => const TransactionsScreen(),
        '/budgets': (context) => const BudgetsScreen(),
        '/categories': (context) => const CategoriesScreen(),
        '/profile': (context) => const ProfileScreen(),
      },
    );
  }
}

class SplashWrapper extends ConsumerWidget {
  const SplashWrapper({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return FutureBuilder<bool>(
      future: _checkSplashEnabled(ref),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Scaffold(
            body: Center(child: CircularProgressIndicator()),
          );
        }

        final splashEnabled = snapshot.data ?? true;
        
        if (splashEnabled) {
          return const SplashScreen();
        } else {
          return const AuthWrapper();
        }
      },
    );
  }

  Future<bool> _checkSplashEnabled(WidgetRef ref) async {
    final storageService = ref.read(storageServiceProvider);
    return await storageService.getSplashEnabled();
  }
}

class AuthWrapper extends ConsumerWidget {
  const AuthWrapper({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final authService = ref.watch(authServiceProvider);
    final themeNotifier = ref.read(themeModeProvider.notifier);
    final languageNotifier = ref.read(languageProvider.notifier);
    final notificationsNotifier = ref.read(
      notificationsEnabledProvider.notifier,
    );

    return FutureBuilder(
      future: Future.wait([
        authService.init(),
        // Charger les préférences après l'initialisation de l'application
        themeNotifier.loadThemeMode(),
        languageNotifier.loadLanguage(),
        notificationsNotifier.loadNotificationsEnabled(),
      ]),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Scaffold(
            body: Center(child: CircularProgressIndicator()),
          );
        }

        if (authService.isLoggedIn) {
          return const DashboardScreen();
        } else {
          return const LoginScreen();
        }
      },
    );
  }
}
