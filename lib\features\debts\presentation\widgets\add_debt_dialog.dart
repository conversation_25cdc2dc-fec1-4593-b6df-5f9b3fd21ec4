import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:image_picker/image_picker.dart';
import '../../domain/debt.dart';
import '../../providers/debt_providers.dart';
import '../../../../core/providers/app_providers.dart';

class AddDebtDialog extends ConsumerStatefulWidget {
  final VoidCallback? onDebtAdded;

  const AddDebtDialog({
    super.key,
    this.onDebtAdded,
  });

  @override
  ConsumerState<AddDebtDialog> createState() => _AddDebtDialogState();
}

class _AddDebtDialogState extends ConsumerState<AddDebtDialog> {
  final _formKey = GlobalKey<FormState>();
  final _creditorController = TextEditingController();
  final _amountController = TextEditingController();
  final _reasonController = TextEditingController();
  final _monthlyAmountController = TextEditingController();
  final _notesController = TextEditingController();

  DebtType _selectedType = DebtType.personal;
  RepaymentType _selectedRepaymentType = RepaymentType.single;
  DateTime _loanDate = DateTime.now();
  DateTime? _dueDate;
  String? _attachmentPath;
  bool _isLoading = false;

  @override
  void dispose() {
    _creditorController.dispose();
    _amountController.dispose();
    _reasonController.dispose();
    _monthlyAmountController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Dialog(
      child: Container(
        constraints: const BoxConstraints(maxWidth: 500, maxHeight: 700),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // En-tête
            Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: theme.colorScheme.primary,
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(12),
                  topRight: Radius.circular(12),
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.add_card,
                    color: theme.colorScheme.onPrimary,
                    size: 24,
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      'Nouvelle Dette',
                      style: theme.textTheme.titleLarge?.copyWith(
                        color: theme.colorScheme.onPrimary,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  IconButton(
                    onPressed: () => Navigator.of(context).pop(),
                    icon: Icon(
                      Icons.close,
                      color: theme.colorScheme.onPrimary,
                    ),
                  ),
                ],
              ),
            ),

            // Contenu
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(20),
                child: Form(
                  key: _formKey,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Créancier
                      TextFormField(
                        controller: _creditorController,
                        decoration: const InputDecoration(
                          labelText: 'Créancier *',
                          hintText: 'Ex: Oncle Michel, Banque ABC...',
                          prefixIcon: Icon(Icons.person),
                          border: OutlineInputBorder(),
                        ),
                        validator: (value) {
                          if (value == null || value.trim().isEmpty) {
                            return 'Le nom du créancier est requis';
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: 16),

                      // Montant
                      TextFormField(
                        controller: _amountController,
                        decoration: const InputDecoration(
                          labelText: 'Montant total (FCFA) *',
                          hintText: 'Ex: 100000',
                          prefixIcon: Icon(Icons.attach_money),
                          border: OutlineInputBorder(),
                        ),
                        keyboardType: TextInputType.number,
                        inputFormatters: [
                          FilteringTextInputFormatter.digitsOnly,
                        ],
                        validator: (value) {
                          if (value == null || value.trim().isEmpty) {
                            return 'Le montant est requis';
                          }
                          final amount = int.tryParse(value);
                          if (amount == null || amount <= 0) {
                            return 'Veuillez entrer un montant valide';
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: 16),

                      // Motif
                      TextFormField(
                        controller: _reasonController,
                        decoration: const InputDecoration(
                          labelText: 'Motif de la dette *',
                          hintText: 'Ex: Frais scolaire, Maladie...',
                          prefixIcon: Icon(Icons.description),
                          border: OutlineInputBorder(),
                        ),
                        maxLines: 2,
                        validator: (value) {
                          if (value == null || value.trim().isEmpty) {
                            return 'Le motif est requis';
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: 16),

                      // Type de dette
                      DropdownButtonFormField<DebtType>(
                        value: _selectedType,
                        decoration: const InputDecoration(
                          labelText: 'Type de dette',
                          prefixIcon: Icon(Icons.category),
                          border: OutlineInputBorder(),
                        ),
                        items: DebtType.values.map((type) {
                          return DropdownMenuItem(
                            value: type,
                            child: Row(
                              children: [
                                Text(type.emoji),
                                const SizedBox(width: 8),
                                Text(type.displayName),
                              ],
                            ),
                          );
                        }).toList(),
                        onChanged: (value) {
                          if (value != null) {
                            setState(() {
                              _selectedType = value;
                            });
                          }
                        },
                      ),
                      const SizedBox(height: 16),

                      // Type de remboursement
                      DropdownButtonFormField<RepaymentType>(
                        value: _selectedRepaymentType,
                        decoration: const InputDecoration(
                          labelText: 'Type de remboursement',
                          prefixIcon: Icon(Icons.payment),
                          border: OutlineInputBorder(),
                        ),
                        items: RepaymentType.values.map((type) {
                          return DropdownMenuItem(
                            value: type,
                            child: Text(type.displayName),
                          );
                        }).toList(),
                        onChanged: (value) {
                          if (value != null) {
                            setState(() {
                              _selectedRepaymentType = value;
                            });
                          }
                        },
                      ),
                      const SizedBox(height: 16),

                      // Montant mensuel (si échelonné)
                      if (_selectedRepaymentType != RepaymentType.single) ...[
                        TextFormField(
                          controller: _monthlyAmountController,
                          decoration: InputDecoration(
                            labelText: 'Montant ${_selectedRepaymentType.displayName.toLowerCase()} (FCFA)',
                            hintText: 'Ex: 25000',
                            prefixIcon: const Icon(Icons.schedule),
                            border: const OutlineInputBorder(),
                          ),
                          keyboardType: TextInputType.number,
                          inputFormatters: [
                            FilteringTextInputFormatter.digitsOnly,
                          ],
                          validator: (value) {
                            if (value != null && value.trim().isNotEmpty) {
                              final amount = int.tryParse(value);
                              if (amount == null || amount <= 0) {
                                return 'Veuillez entrer un montant valide';
                              }
                            }
                            return null;
                          },
                        ),
                        const SizedBox(height: 16),
                      ],

                      // Dates
                      Row(
                        children: [
                          Expanded(
                            child: InkWell(
                              onTap: () => _selectLoanDate(context),
                              child: InputDecorator(
                                decoration: const InputDecoration(
                                  labelText: 'Date d\'emprunt',
                                  prefixIcon: Icon(Icons.calendar_today),
                                  border: OutlineInputBorder(),
                                ),
                                child: Text(_formatDate(_loanDate)),
                              ),
                            ),
                          ),
                          const SizedBox(width: 12),
                          Expanded(
                            child: InkWell(
                              onTap: () => _selectDueDate(context),
                              child: InputDecorator(
                                decoration: const InputDecoration(
                                  labelText: 'Échéance (optionnel)',
                                  prefixIcon: Icon(Icons.event),
                                  border: OutlineInputBorder(),
                                ),
                                child: Text(
                                  _dueDate != null 
                                      ? _formatDate(_dueDate!)
                                      : 'Aucune échéance',
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 16),

                      // Pièce jointe
                      InkWell(
                        onTap: _pickAttachment,
                        child: Container(
                          padding: const EdgeInsets.all(16),
                          decoration: BoxDecoration(
                            border: Border.all(color: Colors.grey),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Row(
                            children: [
                              const Icon(Icons.attach_file),
                              const SizedBox(width: 12),
                              Expanded(
                                child: Text(
                                  _attachmentPath != null
                                      ? 'Pièce jointe ajoutée'
                                      : 'Ajouter une pièce jointe (optionnel)',
                                ),
                              ),
                              if (_attachmentPath != null)
                                IconButton(
                                  onPressed: () {
                                    setState(() {
                                      _attachmentPath = null;
                                    });
                                  },
                                  icon: const Icon(Icons.clear),
                                ),
                            ],
                          ),
                        ),
                      ),
                      const SizedBox(height: 16),

                      // Notes
                      TextFormField(
                        controller: _notesController,
                        decoration: const InputDecoration(
                          labelText: 'Notes (optionnel)',
                          hintText: 'Informations supplémentaires...',
                          prefixIcon: Icon(Icons.note),
                          border: OutlineInputBorder(),
                        ),
                        maxLines: 3,
                      ),
                    ],
                  ),
                ),
              ),
            ),

            // Boutons
            Container(
              padding: const EdgeInsets.all(20),
              child: Row(
                children: [
                  Expanded(
                    child: OutlinedButton(
                      onPressed: _isLoading ? null : () => Navigator.of(context).pop(),
                      child: const Text('Annuler'),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: ElevatedButton(
                      onPressed: _isLoading ? null : _saveDebt,
                      child: _isLoading
                          ? const SizedBox(
                              height: 20,
                              width: 20,
                              child: CircularProgressIndicator(strokeWidth: 2),
                            )
                          : const Text('Enregistrer'),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _selectLoanDate(BuildContext context) async {
    final date = await showDatePicker(
      context: context,
      initialDate: _loanDate,
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
    );
    if (date != null) {
      setState(() {
        _loanDate = date;
      });
    }
  }

  Future<void> _selectDueDate(BuildContext context) async {
    final date = await showDatePicker(
      context: context,
      initialDate: _dueDate ?? DateTime.now().add(const Duration(days: 30)),
      firstDate: DateTime.now(),
      lastDate: DateTime(2030),
    );
    setState(() {
      _dueDate = date;
    });
  }

  Future<void> _pickAttachment() async {
    final ImagePicker picker = ImagePicker();
    final XFile? image = await picker.pickImage(source: ImageSource.gallery);
    
    if (image != null) {
      setState(() {
        _attachmentPath = image.path;
      });
    }
  }

  Future<void> _saveDebt() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final debtService = ref.read(debtServiceProvider);
      final authService = ref.read(authServiceProvider);
      final userId = authService.currentUser?.id ?? 'default';

      final amount = int.parse(_amountController.text) * 100; // Convertir en centimes
      final monthlyAmount = _monthlyAmountController.text.isNotEmpty
          ? int.parse(_monthlyAmountController.text) * 100
          : null;

      await debtService.createDebt(
        userId: userId,
        creditorName: _creditorController.text.trim(),
        totalAmount: amount,
        reason: _reasonController.text.trim(),
        type: _selectedType,
        loanDate: _loanDate,
        dueDate: _dueDate,
        repaymentType: _selectedRepaymentType,
        monthlyAmount: monthlyAmount,
        attachmentPath: _attachmentPath,
        notes: _notesController.text.trim().isNotEmpty 
            ? _notesController.text.trim() 
            : null,
      );

      if (mounted) {
        Navigator.of(context).pop();
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Dette enregistrée avec succès'),
            backgroundColor: Colors.green,
          ),
        );
        widget.onDebtAdded?.call();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Erreur: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day.toString().padLeft(2, '0')}/'
           '${date.month.toString().padLeft(2, '0')}/'
           '${date.year}';
  }
}
