import 'package:flutter/material.dart';

enum AppThemeType { blue, green, purple, orange, pink, teal }

class AppTheme {
  // Noms des thèmes pour l'affichage
  static const Map<AppThemeType, String> themeNames = {
    AppThemeType.blue: 'Océan',
    AppThemeType.green: 'Nature',
    AppThemeType.purple: 'Mystique',
    AppThemeType.orange: 'Coucher de soleil',
    AppThemeType.pink: 'Cerisier',
    AppThemeType.teal: 'Émeraude',
  };

  // Couleurs principales pour chaque thème
  static const Map<AppThemeType, Map<String, Color>> themeColors = {
    AppThemeType.blue: {
      'primary': Color(0xFF3B82F6),
      'accent': Color(0xFF10B981),
      'alert': Color(0xFFEF4444),
      'neutral': Color(0xFF1F2937),
    },
    AppThemeType.green: {
      'primary': Color(0xFF059669),
      'accent': Color(0xFF3B82F6),
      'alert': Color(0xFFDC2626),
      'neutral': Color(0xFF374151),
    },
    AppThemeType.purple: {
      'primary': Color(0xFF7C3AED),
      'accent': Color(0xFF06B6D4),
      'alert': Color(0xFFE11D48),
      'neutral': Color(0xFF4B5563),
    },
    AppThemeType.orange: {
      'primary': Color(0xFFEA580C),
      'accent': Color(0xFF8B5CF6),
      'alert': Color(0xFFDC2626),
      'neutral': Color(0xFF6B7280),
    },
    AppThemeType.pink: {
      'primary': Color(0xFFDB2777),
      'accent': Color(0xFF10B981),
      'alert': Color(0xFFDC2626),
      'neutral': Color(0xFF374151),
    },
    AppThemeType.teal: {
      'primary': Color(0xFF0D9488),
      'accent': Color(0xFF3B82F6),
      'alert': Color(0xFFDC2626),
      'neutral': Color(0xFF1F2937),
    },
  };

  // Thème clair
  static ThemeData getLightTheme(AppThemeType themeType) {
    final colors = themeColors[themeType]!;
    return ThemeData(
      primaryColor: colors['primary']!,
      colorScheme: ColorScheme.light(
        primary: colors['primary']!,
        secondary: colors['accent']!,
        error:
            colors['alert'] ??
            Colors.red, // Ajout d'une valeur par défaut pour éviter l'erreur
        surface: Colors.white,
      ),
      appBarTheme: AppBarTheme(
        backgroundColor: colors['primary']!,
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      cardTheme: CardTheme(
        elevation: 2,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      ),
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: colors['primary']!,
          foregroundColor: Colors.white,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
        ),
      ),
      fontFamily: 'Inter',
      pageTransitionsTheme: const PageTransitionsTheme(
        builders: {
          TargetPlatform.android: CupertinoPageTransitionsBuilder(),
          TargetPlatform.iOS: CupertinoPageTransitionsBuilder(),
          TargetPlatform.windows: FadeUpwardsPageTransitionsBuilder(),
          TargetPlatform.macOS: CupertinoPageTransitionsBuilder(),
        },
      ),
    );
  }

  // Thème sombre
  static ThemeData getDarkTheme(AppThemeType themeType) {
    final colors = themeColors[themeType]!;
    return ThemeData(
      primaryColor: colors['primary']!,
      colorScheme: ColorScheme.dark(
        primary: colors['primary']!,
        secondary: colors['accent']!,
        error: colors['alert'] ?? Colors.red,
        surface: colors['neutral']!,
      ),
      appBarTheme: AppBarTheme(
        backgroundColor: colors['neutral']!,
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      cardTheme: CardTheme(
        elevation: 2,
        color: colors['neutral']!,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      ),
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: colors['primary']!,
          foregroundColor: Colors.white,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
        ),
      ),
      fontFamily: 'Inter',
      pageTransitionsTheme: const PageTransitionsTheme(
        builders: {
          TargetPlatform.android: CupertinoPageTransitionsBuilder(),
          TargetPlatform.iOS: CupertinoPageTransitionsBuilder(),
          TargetPlatform.windows: FadeUpwardsPageTransitionsBuilder(),
          TargetPlatform.macOS: CupertinoPageTransitionsBuilder(),
        },
      ),
    );
  }

  // Couleurs par défaut (pour compatibilité)
  static const Color primaryColor = Color(0xFF3B82F6);
  static const Color accentColor = Color(0xFF10B981);
  static const Color alertColor = Color(0xFFEF4444);
  static const Color neutralColor = Color(0xFF1F2937);

  // Thèmes par défaut (pour compatibilité)
  static final ThemeData lightTheme = getLightTheme(AppThemeType.blue);
  static final ThemeData darkTheme = getDarkTheme(AppThemeType.blue);
}
