import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../providers/debt_providers.dart';

class DebtStatsCard extends ConsumerWidget {
  const DebtStatsCard({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);
    final statsAsync = ref.watch(debtStatsProvider);
    final totalActiveAsync = ref.watch(totalActiveDebtsProvider);
    final totalPaidThisMonthAsync = ref.watch(totalPaidThisMonthProvider);

    return Card(
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.analytics,
                  color: theme.colorScheme.primary,
                  size: 24,
                ),
                const SizedBox(width: 8),
                Text(
                  'Résumé des dettes',
                  style: theme.textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: theme.colorScheme.primary,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            
            // Statistiques principales
            statsAsync.when(
              data: (stats) => Column(
                children: [
                  Row(
                    children: [
                      Expanded(
                        child: _buildStatItem(
                          context,
                          '🔄 Actives',
                          '${stats.totalActiveDebts}',
                          Colors.orange,
                        ),
                      ),
                      Expanded(
                        child: _buildStatItem(
                          context,
                          '✅ Payées',
                          '${stats.totalPaidDebts}',
                          Colors.green,
                        ),
                      ),
                      Expanded(
                        child: _buildStatItem(
                          context,
                          '⚠️ En retard',
                          '${stats.totalOverdueDebts}',
                          Colors.red,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  const Divider(),
                  const SizedBox(height: 16),
                ],
              ),
              loading: () => const Center(
                child: Padding(
                  padding: EdgeInsets.all(16),
                  child: CircularProgressIndicator(),
                ),
              ),
              error: (error, stack) => Center(
                child: Text(
                  'Erreur: $error',
                  style: TextStyle(color: Colors.red[600]),
                ),
              ),
            ),

            // Montants
            Row(
              children: [
                Expanded(
                  child: totalActiveAsync.when(
                    data: (totalActive) => _buildAmountCard(
                      context,
                      'Total dû',
                      _formatAmount(totalActive),
                      Colors.red,
                      Icons.trending_up,
                    ),
                    loading: () => _buildLoadingAmountCard(context, 'Total dû'),
                    error: (error, stack) => _buildErrorAmountCard(context, 'Total dû'),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: totalPaidThisMonthAsync.when(
                    data: (totalPaid) => _buildAmountCard(
                      context,
                      'Payé ce mois',
                      _formatAmount(totalPaid),
                      Colors.green,
                      Icons.trending_down,
                    ),
                    loading: () => _buildLoadingAmountCard(context, 'Payé ce mois'),
                    error: (error, stack) => _buildErrorAmountCard(context, 'Payé ce mois'),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatItem(BuildContext context, String label, String value, Color color) {
    final theme = Theme.of(context);
    
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Text(
            value,
            style: theme.textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            label,
            style: theme.textTheme.bodySmall?.copyWith(
              color: color,
              fontWeight: FontWeight.w500,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildAmountCard(
    BuildContext context,
    String title,
    String amount,
    Color color,
    IconData icon,
  ) {
    final theme = Theme.of(context);
    
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, color: color, size: 20),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  title,
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: color,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            amount,
            style: theme.textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLoadingAmountCard(BuildContext context, String title) {
    final theme = Theme.of(context);
    
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey.withValues(alpha: 0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: theme.textTheme.bodyMedium?.copyWith(
              color: Colors.grey[600],
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 8),
          const SizedBox(
            height: 20,
            width: 20,
            child: CircularProgressIndicator(strokeWidth: 2),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorAmountCard(BuildContext context, String title) {
    final theme = Theme.of(context);
    
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.red.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.red.withValues(alpha: 0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: theme.textTheme.bodyMedium?.copyWith(
              color: Colors.red[600],
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 8),
          Icon(
            Icons.error,
            color: Colors.red[600],
            size: 20,
          ),
        ],
      ),
    );
  }

  String _formatAmount(int amountInCents) {
    final amountInFcfa = amountInCents / 100;
    return '${amountInFcfa.toStringAsFixed(0)} FCFA';
  }
}
