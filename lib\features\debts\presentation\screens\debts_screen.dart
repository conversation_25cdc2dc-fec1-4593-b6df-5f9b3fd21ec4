import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../domain/debt.dart';
import '../../providers/debt_providers.dart';
import '../widgets/debt_card.dart';
import '../widgets/debt_stats_card.dart';
import '../widgets/add_debt_dialog.dart';
import '../widgets/debt_filter_tabs.dart';

class DebtsScreen extends ConsumerStatefulWidget {
  const DebtsScreen({super.key});

  @override
  ConsumerState<DebtsScreen> createState() => _DebtsScreenState();
}

class _DebtsScreenState extends ConsumerState<DebtsScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  int _currentTabIndex = 0;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _tabController.addListener(() {
      setState(() {
        _currentTabIndex = _tabController.index;
      });
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      appBar: AppBar(
        title: const Text('💳 Mes Dettes'),
        backgroundColor: theme.colorScheme.surface,
        foregroundColor: theme.colorScheme.onSurface,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () => refreshAllDebtProviders(ref),
            tooltip: 'Actualiser',
          ),
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: () => _showAddDebtDialog(context),
            tooltip: 'Ajouter une dette',
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(
              icon: Icon(Icons.list),
              text: 'Toutes',
            ),
            Tab(
              icon: Icon(Icons.pending_actions),
              text: 'Actives',
            ),
            Tab(
              icon: Icon(Icons.warning),
              text: 'En retard',
            ),
            Tab(
              icon: Icon(Icons.check_circle),
              text: 'Payées',
            ),
          ],
          labelColor: theme.colorScheme.primary,
          unselectedLabelColor: theme.colorScheme.onSurface.withValues(alpha: 0.6),
          indicatorColor: theme.colorScheme.primary,
        ),
      ),
      body: Column(
        children: [
          // Statistiques
          const Padding(
            padding: EdgeInsets.all(16.0),
            child: DebtStatsCard(),
          ),
          
          // Contenu des onglets
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                _buildAllDebtsTab(),
                _buildActiveDebtsTab(),
                _buildOverdueDebtsTab(),
                _buildPaidDebtsTab(),
              ],
            ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () => _showAddDebtDialog(context),
        backgroundColor: theme.colorScheme.primary,
        foregroundColor: theme.colorScheme.onPrimary,
        child: const Icon(Icons.add),
      ),
    );
  }

  Widget _buildAllDebtsTab() {
    return Consumer(
      builder: (context, ref, child) {
        final debtsAsync = ref.watch(userDebtsProvider);
        
        return debtsAsync.when(
          data: (debts) => _buildDebtsList(debts, 'Aucune dette enregistrée'),
          loading: () => const Center(child: CircularProgressIndicator()),
          error: (error, stack) => Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(Icons.error, size: 64, color: Colors.red),
                const SizedBox(height: 16),
                Text('Erreur: $error'),
                const SizedBox(height: 16),
                ElevatedButton(
                  onPressed: () => ref.refresh(userDebtsProvider),
                  child: const Text('Réessayer'),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildActiveDebtsTab() {
    return Consumer(
      builder: (context, ref, child) {
        final debtsAsync = ref.watch(activeDebtsProvider);
        
        return debtsAsync.when(
          data: (debts) => _buildDebtsList(debts, 'Aucune dette active'),
          loading: () => const Center(child: CircularProgressIndicator()),
          error: (error, stack) => Center(
            child: Text('Erreur: $error'),
          ),
        );
      },
    );
  }

  Widget _buildOverdueDebtsTab() {
    return Consumer(
      builder: (context, ref, child) {
        final debtsAsync = ref.watch(overdueDebtsProvider);
        
        return debtsAsync.when(
          data: (debts) => _buildDebtsList(debts, 'Aucune dette en retard'),
          loading: () => const Center(child: CircularProgressIndicator()),
          error: (error, stack) => Center(
            child: Text('Erreur: $error'),
          ),
        );
      },
    );
  }

  Widget _buildPaidDebtsTab() {
    return Consumer(
      builder: (context, ref, child) {
        final debtsAsync = ref.watch(paidDebtsProvider);
        
        return debtsAsync.when(
          data: (debts) => _buildDebtsList(debts, 'Aucune dette remboursée'),
          loading: () => const Center(child: CircularProgressIndicator()),
          error: (error, stack) => Center(
            child: Text('Erreur: $error'),
          ),
        );
      },
    );
  }

  Widget _buildDebtsList(List<Debt> debts, String emptyMessage) {
    if (debts.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.credit_card_off,
              size: 64,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              emptyMessage,
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: () => _showAddDebtDialog(context),
              icon: const Icon(Icons.add),
              label: const Text('Ajouter une dette'),
            ),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: () async {
        refreshAllDebtProviders(ref);
      },
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: debts.length,
        itemBuilder: (context, index) {
          final debt = debts[index];
          return Padding(
            padding: const EdgeInsets.only(bottom: 12),
            child: DebtCard(
              debt: debt,
              onTap: () => _navigateToDebtDetails(debt),
            ),
          );
        },
      ),
    );
  }

  void _showAddDebtDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AddDebtDialog(
        onDebtAdded: () {
          refreshAllDebtProviders(ref);
        },
      ),
    );
  }

  void _navigateToDebtDetails(Debt debt) {
    Navigator.of(context).pushNamed(
      '/debt-details',
      arguments: debt.id,
    );
  }
}
