import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../domain/fixed_charge.dart';
import '../domain/charge_payment.dart';
import '../services/fixed_charge_service.dart';
import '../../../core/providers/app_providers.dart';

// Provider pour le service de charges fixes
final fixedChargeServiceProvider = Provider<FixedChargeService>((ref) {
  final storageService = ref.read(storageServiceProvider);
  return FixedChargeService(storageService);
});

// Provider pour toutes les charges fixes de l'utilisateur
final userFixedChargesProvider = FutureProvider<List<FixedCharge>>((ref) async {
  final chargeService = ref.read(fixedChargeServiceProvider);
  final authService = ref.read(authServiceProvider);
  final userId = authService.currentUser?.id;
  
  return chargeService.getUserFixedCharges(userId);
});

// Provider pour les charges fixes actives
final activeFixedChargesProvider = FutureProvider<List<FixedCharge>>((ref) async {
  final chargeService = ref.read(fixedChargeServiceProvider);
  final authService = ref.read(authServiceProvider);
  final userId = authService.currentUser?.id;
  
  return chargeService.getActiveFixedCharges(userId);
});

// Provider pour les charges fixes par type
final fixedChargesByTypeProvider = FutureProvider.family<List<FixedCharge>, ChargeType>((ref, type) async {
  final chargeService = ref.read(fixedChargeServiceProvider);
  final authService = ref.read(authServiceProvider);
  final userId = authService.currentUser?.id;
  
  return chargeService.getFixedChargesByType(userId, type);
});

// Provider pour les charges fixes par statut
final fixedChargesByStatusProvider = FutureProvider.family<List<FixedCharge>, ChargeStatus>((ref, status) async {
  final chargeService = ref.read(fixedChargeServiceProvider);
  final authService = ref.read(authServiceProvider);
  final userId = authService.currentUser?.id;
  
  return chargeService.getFixedChargesByStatus(userId, status);
});

// Provider pour les charges fixes en retard
final overdueFixedChargesProvider = FutureProvider<List<FixedCharge>>((ref) async {
  final chargeService = ref.read(fixedChargeServiceProvider);
  final authService = ref.read(authServiceProvider);
  final userId = authService.currentUser?.id;
  
  return chargeService.getOverdueFixedCharges(userId);
});

// Provider pour les charges fixes avec échéance proche
final dueSoonFixedChargesProvider = FutureProvider<List<FixedCharge>>((ref) async {
  final chargeService = ref.read(fixedChargeServiceProvider);
  final authService = ref.read(authServiceProvider);
  final userId = authService.currentUser?.id;
  
  return chargeService.getDueSoonFixedCharges(userId);
});

// Provider pour une charge fixe spécifique
final fixedChargeProvider = FutureProvider.family<FixedCharge?, String>((ref, chargeId) async {
  final chargeService = ref.read(fixedChargeServiceProvider);
  return chargeService.getFixedChargeById(chargeId);
});

// Provider pour les paiements d'une charge
final chargePaymentsProvider = FutureProvider.family<List<ChargePayment>, String>((ref, chargeId) async {
  final chargeService = ref.read(fixedChargeServiceProvider);
  return chargeService.getPaymentsByChargeId(chargeId);
});

// Provider pour le résumé des charges fixes
final fixedChargesSummaryProvider = FutureProvider<FixedChargesSummary>((ref) async {
  final chargeService = ref.read(fixedChargeServiceProvider);
  final authService = ref.read(authServiceProvider);
  final userId = authService.currentUser?.id;
  
  return chargeService.getFixedChargesSummary(userId);
});

// Provider pour les charges à venir
final upcomingChargesProvider = FutureProvider<List<FixedCharge>>((ref) async {
  final chargeService = ref.read(fixedChargeServiceProvider);
  final authService = ref.read(authServiceProvider);
  final userId = authService.currentUser?.id;
  
  return chargeService.getUpcomingCharges(userId);
});

// Provider pour les charges à venir dans les 7 prochains jours
final upcomingChargesWeekProvider = FutureProvider<List<FixedCharge>>((ref) async {
  final chargeService = ref.read(fixedChargeServiceProvider);
  final authService = ref.read(authServiceProvider);
  final userId = authService.currentUser?.id;
  
  return chargeService.getUpcomingCharges(userId, daysAhead: 7);
});

// Provider pour le total par type
final totalByTypeProvider = FutureProvider<Map<ChargeType, int>>((ref) async {
  final chargeService = ref.read(fixedChargeServiceProvider);
  final authService = ref.read(authServiceProvider);
  final userId = authService.currentUser?.id;
  
  return chargeService.getTotalByType(userId);
});

// Provider pour les statistiques générales des charges fixes
final fixedChargesStatsProvider = FutureProvider<FixedChargesStats>((ref) async {
  final chargeService = ref.read(fixedChargeServiceProvider);
  final authService = ref.read(authServiceProvider);
  final userId = authService.currentUser?.id;
  
  final summary = chargeService.getFixedChargesSummary(userId);
  final activeCharges = chargeService.getActiveFixedCharges(userId);
  final overdueCharges = chargeService.getOverdueFixedCharges(userId);
  final dueSoonCharges = chargeService.getDueSoonFixedCharges(userId);
  final upcomingCharges = chargeService.getUpcomingCharges(userId);
  
  return FixedChargesStats(
    totalActiveCharges: activeCharges.length,
    totalOverdueCharges: overdueCharges.length,
    totalDueSoonCharges: dueSoonCharges.length,
    totalUpcomingCharges: upcomingCharges.length,
    totalMonthlyAmount: summary.totalMonthlyAmount,
    totalPaidThisMonth: summary.totalPaidThisMonth,
    paymentProgressPercentage: summary.paymentProgressPercentage,
  );
});

// Classe pour les statistiques des charges fixes
class FixedChargesStats {
  final int totalActiveCharges;
  final int totalOverdueCharges;
  final int totalDueSoonCharges;
  final int totalUpcomingCharges;
  final int totalMonthlyAmount;
  final int totalPaidThisMonth;
  final double paymentProgressPercentage;

  FixedChargesStats({
    required this.totalActiveCharges,
    required this.totalOverdueCharges,
    required this.totalDueSoonCharges,
    required this.totalUpcomingCharges,
    required this.totalMonthlyAmount,
    required this.totalPaidThisMonth,
    required this.paymentProgressPercentage,
  });

  String get formattedTotalMonthlyAmount {
    final amountInFcfa = totalMonthlyAmount / 100;
    return '${amountInFcfa.toStringAsFixed(0)} FCFA';
  }

  String get formattedTotalPaidThisMonth {
    final amountInFcfa = totalPaidThisMonth / 100;
    return '${amountInFcfa.toStringAsFixed(0)} FCFA';
  }

  String get formattedPaymentProgressPercentage {
    return '${paymentProgressPercentage.toStringAsFixed(1)}%';
  }

  bool get hasOverdueCharges => totalOverdueCharges > 0;
  bool get hasDueSoonCharges => totalDueSoonCharges > 0;
  bool get hasUpcomingCharges => totalUpcomingCharges > 0;
}

// Provider pour rafraîchir les données
final refreshFixedChargesProvider = StateProvider<int>((ref) => 0);

// Méthode utilitaire pour rafraîchir tous les providers de charges fixes
void refreshAllFixedChargeProviders(WidgetRef ref) {
  ref.invalidate(userFixedChargesProvider);
  ref.invalidate(activeFixedChargesProvider);
  ref.invalidate(overdueFixedChargesProvider);
  ref.invalidate(dueSoonFixedChargesProvider);
  ref.invalidate(upcomingChargesProvider);
  ref.invalidate(upcomingChargesWeekProvider);
  ref.invalidate(fixedChargesSummaryProvider);
  ref.invalidate(totalByTypeProvider);
  ref.invalidate(fixedChargesStatsProvider);
  
  // Incrémenter le compteur de rafraîchissement
  ref.read(refreshFixedChargesProvider.notifier).state++;
}
