import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../transactions/presentation/widgets/add_transaction_modal.dart';
import '../../../budgets/presentation/widgets/add_budget_dialog.dart';
import '../../../categories/presentation/widgets/add_category_dialog.dart';
import '../../../debts/presentation/widgets/add_debt_dialog.dart';

class FloatingActionMenu extends ConsumerStatefulWidget {
  final int currentIndex;
  
  const FloatingActionMenu({
    super.key,
    required this.currentIndex,
  });

  @override
  ConsumerState<FloatingActionMenu> createState() => _FloatingActionMenuState();
}

class _FloatingActionMenuState extends ConsumerState<FloatingActionMenu>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _animation;
  bool _isMenuOpen = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _animation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    );
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _toggleMenu() {
    setState(() {
      _isMenuOpen = !_isMenuOpen;
    });
    if (_isMenuOpen) {
      _animationController.forward();
    } else {
      _animationController.reverse();
    }
  }

  void _closeMenu() {
    if (_isMenuOpen) {
      setState(() {
        _isMenuOpen = false;
      });
      _animationController.reverse();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      alignment: Alignment.bottomRight,
      children: [
        // Overlay pour fermer le menu
        if (_isMenuOpen)
          GestureDetector(
            onTap: _closeMenu,
            child: Container(
              width: double.infinity,
              height: double.infinity,
              color: Colors.black.withOpacity(0.3),
            ),
          ),
        
        // Menu items
        ..._buildMenuItems(),
        
        // Main FAB
        FloatingActionButton(
          onPressed: _toggleMenu,
          backgroundColor: AppTheme.primaryColor,
          child: AnimatedRotation(
            turns: _isMenuOpen ? 0.125 : 0,
            duration: const Duration(milliseconds: 300),
            child: Icon(
              _isMenuOpen ? Icons.close : Icons.add,
              color: Colors.white,
            ),
          ),
        ),
      ],
    );
  }

  List<Widget> _buildMenuItems() {
    final items = <Widget>[];
    final menuItems = _getMenuItemsForCurrentScreen();
    
    for (int i = 0; i < menuItems.length; i++) {
      items.add(
        AnimatedBuilder(
          animation: _animation,
          builder: (context, child) {
            final offset = (i + 1) * 70.0 * _animation.value;
            return Positioned(
              bottom: offset,
              right: 0,
              child: Transform.scale(
                scale: _animation.value,
                child: Opacity(
                  opacity: _animation.value,
                  child: _buildMenuItem(
                    menuItems[i]['icon'] as IconData,
                    menuItems[i]['label'] as String,
                    menuItems[i]['color'] as Color,
                    menuItems[i]['onTap'] as VoidCallback,
                  ),
                ),
              ),
            );
          },
        ),
      );
    }
    
    return items;
  }

  Widget _buildMenuItem(
    IconData icon,
    String label,
    Color color,
    VoidCallback onTap,
  ) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(20),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.1),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Text(
            label,
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
        const SizedBox(width: 8),
        FloatingActionButton(
          mini: true,
          onPressed: () {
            _closeMenu();
            onTap();
          },
          backgroundColor: color,
          child: Icon(icon, color: Colors.white, size: 20),
        ),
      ],
    );
  }

  List<Map<String, dynamic>> _getMenuItemsForCurrentScreen() {
    switch (widget.currentIndex) {
      case 0: // Dashboard
        return [
          {
            'icon': Icons.add,
            'label': 'Transaction',
            'color': Colors.green,
            'onTap': () => _showAddTransactionModal(),
          },
          {
            'icon': Icons.account_balance_wallet,
            'label': 'Budget',
            'color': Colors.blue,
            'onTap': () => _showAddBudgetDialog(),
          },
        ];
      case 1: // Transactions
        return [
          {
            'icon': Icons.add,
            'label': 'Transaction',
            'color': Colors.green,
            'onTap': () => _showAddTransactionModal(),
          },
        ];
      case 2: // Budgets
        return [
          {
            'icon': Icons.account_balance_wallet,
            'label': 'Budget',
            'color': Colors.blue,
            'onTap': () => _showAddBudgetDialog(),
          },
        ];
      case 3: // Categories
        return [
          {
            'icon': Icons.category,
            'label': 'Catégorie',
            'color': Colors.orange,
            'onTap': () => _showAddCategoryDialog(),
          },
        ];
      case 4: // Debts
        return [
          {
            'icon': Icons.credit_card,
            'label': 'Dette',
            'color': Colors.red,
            'onTap': () => _showAddDebtDialog(),
          },
        ];
      default:
        return [];
    }
  }

  void _showAddTransactionModal() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => const AddTransactionModal(),
    );
  }

  void _showAddBudgetDialog() {
    showDialog(
      context: context,
      builder: (context) => const AddBudgetDialog(),
    );
  }

  void _showAddCategoryDialog() {
    showDialog(
      context: context,
      builder: (context) => const AddCategoryDialog(),
    );
  }

  void _showAddDebtDialog() {
    showDialog(
      context: context,
      builder: (context) => const AddDebtDialog(),
    );
  }
}