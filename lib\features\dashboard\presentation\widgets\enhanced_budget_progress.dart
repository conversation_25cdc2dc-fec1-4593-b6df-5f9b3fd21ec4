import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../core/utils/currency_formatter.dart';

class EnhancedBudgetProgress extends ConsumerStatefulWidget {
  final String categoryName;
  final double spent;
  final double budget;
  final Color color;
  final IconData icon;

  const EnhancedBudgetProgress({
    super.key,
    required this.categoryName,
    required this.spent,
    required this.budget,
    required this.color,
    required this.icon,
  });

  @override
  ConsumerState<EnhancedBudgetProgress> createState() =>
      _EnhancedBudgetProgressState();
}

class _EnhancedBudgetProgressState extends ConsumerState<EnhancedBudgetProgress>
    with TickerProviderStateMixin {
  late AnimationController _progressController;
  late AnimationController _pulseController;
  late Animation<double> _progressAnimation;
  late Animation<double> _pulseAnimation;
  bool _isExpanded = false;

  @override
  void initState() {
    super.initState();

    _progressController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );

    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );

    _progressAnimation = Tween<double>(
      begin: 0.0,
      end: (widget.spent / widget.budget).clamp(0.0, 1.0),
    ).animate(
      CurvedAnimation(parent: _progressController, curve: Curves.easeOutCubic),
    );

    _pulseAnimation = Tween<double>(begin: 1.0, end: 1.1).animate(
      CurvedAnimation(parent: _pulseController, curve: Curves.easeInOut),
    );

    // Démarrer l'animation de progression
    Future.delayed(const Duration(milliseconds: 300), () {
      if (mounted) {
        _progressController.forward();
      }
    });

    // Animation de pulsation si le budget est dépassé
    if (widget.spent > widget.budget) {
      _pulseController.repeat(reverse: true);
    }
  }

  @override
  void dispose() {
    _progressController.dispose();
    _pulseController.dispose();
    super.dispose();
  }

  double get progressPercentage =>
      (widget.spent / widget.budget * 100).clamp(0.0, 100.0);
  bool get isOverBudget => widget.spent > widget.budget;
  double get remainingBudget => widget.budget - widget.spent;

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: Listenable.merge([_progressController, _pulseController]),
      builder: (context, child) {
        return Transform.scale(
          scale: isOverBudget ? _pulseAnimation.value : 1.0,
          child: GestureDetector(
            onTap: () {
              setState(() {
                _isExpanded = !_isExpanded;
              });
            },
            child: AnimatedContainer(
              duration: const Duration(milliseconds: 300),
              margin: const EdgeInsets.symmetric(vertical: 8),
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(16),
                border: Border.all(
                  color:
                      isOverBudget
                          ? Colors.red.withOpacity(0.3)
                          : widget.color.withOpacity(0.2),
                  width: 2,
                ),
                boxShadow: [
                  BoxShadow(
                    color: widget.color.withOpacity(0.1),
                    blurRadius: 10,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: Column(
                children: [
                  _buildHeader(),
                  const SizedBox(height: 12),
                  _buildProgressBar(),
                  const SizedBox(height: 8),
                  _buildProgressInfo(),
                  if (_isExpanded) ...[
                    const SizedBox(height: 16),
                    _buildExpandedContent(),
                  ],
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildHeader() {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(10),
          decoration: BoxDecoration(
            color: widget.color.withOpacity(0.1),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Icon(widget.icon, color: widget.color, size: 24),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                widget.categoryName,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: Colors.black87,
                ),
              ),
              const SizedBox(height: 2),
              Text(
                '${CurrencyFormatter.format(widget.spent.toInt())} / ${CurrencyFormatter.format(widget.budget.toInt())}',
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey[600],
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ),
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          decoration: BoxDecoration(
            color:
                isOverBudget
                    ? Colors.red.withOpacity(0.1)
                    : widget.color.withOpacity(0.1),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Text(
            '${progressPercentage.toStringAsFixed(0)}%',
            style: TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.bold,
              color: isOverBudget ? Colors.red : widget.color,
            ),
          ),
        ),
        const SizedBox(width: 8),
        AnimatedRotation(
          turns: _isExpanded ? 0.5 : 0,
          duration: const Duration(milliseconds: 300),
          child: Icon(Icons.keyboard_arrow_down, color: Colors.grey[600]),
        ),
      ],
    );
  }

  Widget _buildProgressBar() {
    return Container(
      height: 8,
      decoration: BoxDecoration(
        color: Colors.grey[200],
        borderRadius: BorderRadius.circular(4),
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(4),
        child: Stack(
          children: [
            // Barre de progression principale
            FractionallySizedBox(
              widthFactor: _progressAnimation.value,
              child: Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors:
                        isOverBudget
                            ? [Colors.red, Colors.red.shade700]
                            : [widget.color.withOpacity(0.7), widget.color],
                  ),
                ),
              ),
            ),
            // Effet de brillance
            if (_progressAnimation.value > 0)
              Positioned(
                left:
                    (_progressAnimation.value *
                        MediaQuery.of(context).size.width *
                        0.8) -
                    20,
                child: Container(
                  width: 20,
                  height: 8,
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [
                        Colors.white.withOpacity(0),
                        Colors.white.withOpacity(0.6),
                        Colors.white.withOpacity(0),
                      ],
                    ),
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildProgressInfo() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          isOverBudget
              ? 'Dépassement: ${CurrencyFormatter.format((widget.spent - widget.budget).toInt())}'
              : 'Restant: ${CurrencyFormatter.format(remainingBudget.toInt())}',
          style: TextStyle(
            fontSize: 12,
            color: isOverBudget ? Colors.red : Colors.grey[600],
            fontWeight: FontWeight.w500,
          ),
        ),
        if (isOverBudget)
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
            decoration: BoxDecoration(
              color: Colors.red.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(Icons.warning_amber_rounded, size: 12, color: Colors.red),
                const SizedBox(width: 2),
                Text(
                  'Dépassé',
                  style: TextStyle(
                    fontSize: 10,
                    color: Colors.red,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
      ],
    );
  }

  Widget _buildExpandedContent() {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: widget.color.withOpacity(0.05),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        children: [
          Row(
            children: [
              Expanded(
                child: _buildStatItem(
                  'Dépensé',
                  CurrencyFormatter.format(widget.spent.toInt()),
                  isOverBudget ? Colors.red : widget.color,
                ),
              ),
              Container(width: 1, height: 30, color: Colors.grey[300]),
              Expanded(
                child: _buildStatItem(
                  'Budget',
                  CurrencyFormatter.format(widget.budget.toInt()),
                  Colors.grey[600]!,
                ),
              ),
              Container(width: 1, height: 30, color: Colors.grey[300]),
              Expanded(
                child: _buildStatItem(
                  isOverBudget ? 'Dépassement' : 'Restant',
                  CurrencyFormatter.format(
                    isOverBudget
                        ? widget.spent - widget.budget
                        : remainingBudget,
                  ),
                  isOverBudget ? Colors.red : Colors.green,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: () {
                    // Action pour voir les détails
                  },
                  icon: const Icon(Icons.visibility_outlined, size: 16),
                  label: const Text('Détails'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: widget.color.withOpacity(0.1),
                    foregroundColor: widget.color,
                    elevation: 0,
                    padding: const EdgeInsets.symmetric(vertical: 8),
                  ),
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: () {
                    // Action pour ajuster le budget
                  },
                  icon: const Icon(Icons.edit_outlined, size: 16),
                  label: const Text('Ajuster'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.grey[100],
                    foregroundColor: Colors.grey[700],
                    elevation: 0,
                    padding: const EdgeInsets.symmetric(vertical: 8),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatItem(String label, String value, Color color) {
    return Column(
      children: [
        Text(
          label,
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey[600],
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          value,
          style: TextStyle(
            fontSize: 14,
            color: color,
            fontWeight: FontWeight.bold,
          ),
        ),
      ],
    );
  }
}
