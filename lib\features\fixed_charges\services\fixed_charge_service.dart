import 'package:uuid/uuid.dart';
import '../../../core/services/storage_service.dart';
import '../domain/fixed_charge.dart';
import '../domain/charge_payment.dart';

class FixedChargeService {
  final StorageService _storage;
  final Uuid _uuid = const Uuid();

  FixedChargeService(this._storage);

  // ==================== GESTION DES CHARGES FIXES ====================

  /// Créer une nouvelle charge fixe
  Future<FixedCharge> createFixedCharge({
    required String userId,
    required String name,
    required int amount,
    required int dayOfMonth,
    required ChargeRecurrence recurrence,
    String? categoryId,
    String? description,
    required ChargeType type,
    bool reminderEnabled = true,
    int reminderDaysBefore = 2,
  }) async {
    final charge = FixedCharge(
      id: _uuid.v4(),
      userId: userId,
      name: name,
      amount: amount,
      dayOfMonth: dayOfMonth,
      recurrence: recurrence,
      categoryId: categoryId,
      description: description,
      isActive: true,
      reminderEnabled: reminderEnabled,
      reminderDaysBefore: reminderDaysBefore,
      type: type,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );

    await _storage.saveFixedCharge(charge);
    return charge;
  }

  /// Obtenir toutes les charges fixes d'un utilisateur
  List<FixedCharge> getUserFixedCharges(String? userId) {
    final allCharges = _storage.getAllFixedCharges();
    if (userId == null) return allCharges;
    return allCharges.where((charge) => charge.userId == userId).toList();
  }

  /// Obtenir les charges fixes actives
  List<FixedCharge> getActiveFixedCharges(String? userId) {
    return getUserFixedCharges(userId)
        .where((charge) => charge.isActive)
        .toList();
  }

  /// Obtenir les charges fixes par type
  List<FixedCharge> getFixedChargesByType(String? userId, ChargeType type) {
    return getUserFixedCharges(userId)
        .where((charge) => charge.type == type && charge.isActive)
        .toList();
  }

  /// Obtenir les charges fixes par statut
  List<FixedCharge> getFixedChargesByStatus(String? userId, ChargeStatus status) {
    return getUserFixedCharges(userId)
        .where((charge) => charge.status == status)
        .toList();
  }

  /// Obtenir les charges fixes en retard
  List<FixedCharge> getOverdueFixedCharges(String? userId) {
    return getFixedChargesByStatus(userId, ChargeStatus.overdue);
  }

  /// Obtenir les charges fixes avec échéance proche
  List<FixedCharge> getDueSoonFixedCharges(String? userId) {
    return getFixedChargesByStatus(userId, ChargeStatus.dueSoon);
  }

  /// Obtenir une charge fixe par ID
  FixedCharge? getFixedChargeById(String chargeId) {
    final allCharges = _storage.getAllFixedCharges();
    try {
      return allCharges.firstWhere((charge) => charge.id == chargeId);
    } catch (e) {
      return null;
    }
  }

  /// Mettre à jour une charge fixe
  Future<void> updateFixedCharge(FixedCharge charge) async {
    final updatedCharge = charge.copyWith(updatedAt: DateTime.now());
    await _storage.saveFixedCharge(updatedCharge);
  }

  /// Supprimer une charge fixe
  Future<void> deleteFixedCharge(String chargeId) async {
    await _storage.deleteFixedCharge(chargeId);
    
    // Supprimer aussi tous les paiements associés
    final payments = getPaymentsByChargeId(chargeId);
    for (final payment in payments) {
      await _storage.deleteChargePayment(payment.id);
    }
  }

  /// Activer/Désactiver une charge fixe
  Future<void> toggleFixedChargeStatus(String chargeId) async {
    final charge = getFixedChargeById(chargeId);
    if (charge != null) {
      final updatedCharge = charge.copyWith(
        isActive: !charge.isActive,
        updatedAt: DateTime.now(),
      );
      await updateFixedCharge(updatedCharge);
    }
  }

  // ==================== GESTION DES PAIEMENTS ====================

  /// Marquer une charge comme payée
  Future<ChargePayment> markChargeAsPaid({
    required String chargeId,
    required DateTime paymentDate,
    int? customAmount,
    String? notes,
    String? transactionId,
  }) async {
    final charge = getFixedChargeById(chargeId);
    if (charge == null) {
      throw Exception('Charge fixe non trouvée');
    }

    final payment = ChargePayment(
      id: _uuid.v4(),
      fixedChargeId: chargeId,
      amount: customAmount ?? charge.amount,
      paymentDate: paymentDate,
      dueDate: charge.nextDueDate,
      status: PaymentStatus.paid,
      notes: notes,
      transactionId: transactionId,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );

    await _storage.saveChargePayment(payment);

    // Mettre à jour la date de dernier paiement de la charge
    final updatedCharge = charge.copyWith(
      lastPaidDate: paymentDate,
      updatedAt: DateTime.now(),
    );
    await updateFixedCharge(updatedCharge);

    return payment;
  }

  /// Obtenir tous les paiements d'une charge
  List<ChargePayment> getPaymentsByChargeId(String chargeId) {
    final allPayments = _storage.getAllChargePayments();
    return allPayments
        .where((payment) => payment.fixedChargeId == chargeId)
        .toList()
      ..sort((a, b) => b.paymentDate.compareTo(a.paymentDate));
  }

  /// Obtenir un paiement par ID
  ChargePayment? getPaymentById(String paymentId) {
    final allPayments = _storage.getAllChargePayments();
    try {
      return allPayments.firstWhere((payment) => payment.id == paymentId);
    } catch (e) {
      return null;
    }
  }

  /// Mettre à jour un paiement
  Future<void> updatePayment(ChargePayment payment) async {
    final updatedPayment = payment.copyWith(updatedAt: DateTime.now());
    await _storage.saveChargePayment(updatedPayment);
  }

  /// Supprimer un paiement
  Future<void> deletePayment(String paymentId) async {
    await _storage.deleteChargePayment(paymentId);
  }

  // ==================== CALCULS ET STATISTIQUES ====================

  /// Obtenir le résumé des charges fixes
  FixedChargesSummary getFixedChargesSummary(String? userId) {
    final activeCharges = getActiveFixedCharges(userId);
    final overdueCharges = getOverdueFixedCharges(userId);
    final dueSoonCharges = getDueSoonFixedCharges(userId);
    
    // Calculer le total mensuel (convertir toutes les récurrences en montant mensuel)
    int totalMonthlyAmount = 0;
    for (final charge in activeCharges) {
      switch (charge.recurrence) {
        case ChargeRecurrence.weekly:
          totalMonthlyAmount += (charge.amount * 4.33).round(); // ~4.33 semaines par mois
          break;
        case ChargeRecurrence.monthly:
          totalMonthlyAmount += charge.amount;
          break;
        case ChargeRecurrence.quarterly:
          totalMonthlyAmount += (charge.amount / 3).round();
          break;
        case ChargeRecurrence.yearly:
          totalMonthlyAmount += (charge.amount / 12).round();
          break;
      }
    }

    // Calculer le total payé ce mois
    final now = DateTime.now();
    final startOfMonth = DateTime(now.year, now.month, 1);
    final endOfMonth = DateTime(now.year, now.month + 1, 0);
    
    int totalPaidThisMonth = 0;
    final recentPayments = <ChargePayment>[];
    
    for (final charge in activeCharges) {
      final payments = getPaymentsByChargeId(charge.id);
      for (final payment in payments) {
        if (payment.paymentDate.isAfter(startOfMonth) &&
            payment.paymentDate.isBefore(endOfMonth.add(const Duration(days: 1)))) {
          totalPaidThisMonth += payment.amount;
          recentPayments.add(payment);
        }
      }
    }

    // Trier les paiements récents par date
    recentPayments.sort((a, b) => b.paymentDate.compareTo(a.paymentDate));

    return FixedChargesSummary(
      totalActiveCharges: activeCharges.length,
      totalMonthlyAmount: totalMonthlyAmount,
      totalPaidThisMonth: totalPaidThisMonth,
      totalOverdueCharges: overdueCharges.length,
      totalDueSoonCharges: dueSoonCharges.length,
      recentPayments: recentPayments.take(10).toList(), // Les 10 derniers paiements
    );
  }

  /// Obtenir les charges à payer dans les prochains jours
  List<FixedCharge> getUpcomingCharges(String? userId, {int daysAhead = 7}) {
    final activeCharges = getActiveFixedCharges(userId);
    final now = DateTime.now();
    final futureDate = now.add(Duration(days: daysAhead));

    return activeCharges.where((charge) {
      final nextDue = charge.nextDueDate;
      return nextDue.isAfter(now) && nextDue.isBefore(futureDate);
    }).toList()
      ..sort((a, b) => a.nextDueDate.compareTo(b.nextDueDate));
  }

  /// Obtenir le total des charges par type
  Map<ChargeType, int> getTotalByType(String? userId) {
    final activeCharges = getActiveFixedCharges(userId);
    final totals = <ChargeType, int>{};

    for (final type in ChargeType.values) {
      totals[type] = 0;
    }

    for (final charge in activeCharges) {
      totals[charge.type] = (totals[charge.type] ?? 0) + charge.amount;
    }

    return totals;
  }

  /// Vérifier les charges en retard et mettre à jour leur statut
  Future<void> updateOverdueCharges(String? userId) async {
    final activeCharges = getActiveFixedCharges(userId);
    
    for (final charge in activeCharges) {
      if (charge.isOverdue) {
        // Ici, on pourrait créer une notification ou déclencher une action
        // Pour l'instant, le statut est calculé dynamiquement
      }
    }
  }

  /// Générer les paiements automatiques pour le mois
  Future<List<ChargePayment>> generateMonthlyPayments(String? userId, DateTime month) async {
    final activeCharges = getActiveFixedCharges(userId);
    final payments = <ChargePayment>[];

    for (final charge in activeCharges) {
      if (charge.recurrence == ChargeRecurrence.monthly) {
        final dueDate = DateTime(month.year, month.month, charge.dayOfMonth);
        
        // Vérifier si un paiement n'existe pas déjà pour cette période
        final existingPayments = getPaymentsByChargeId(charge.id);
        final hasPaymentForMonth = existingPayments.any((payment) =>
            payment.dueDate.year == month.year &&
            payment.dueDate.month == month.month);

        if (!hasPaymentForMonth) {
          final payment = ChargePayment(
            id: _uuid.v4(),
            fixedChargeId: charge.id,
            amount: charge.amount,
            paymentDate: dueDate,
            dueDate: dueDate,
            status: PaymentStatus.pending,
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
          );
          
          await _storage.saveChargePayment(payment);
          payments.add(payment);
        }
      }
    }

    return payments;
  }
}
