import 'package:uuid/uuid.dart';
import '../../features/budgets/domain/budget.dart';
import 'storage_service.dart';

class BudgetService {
  final StorageService _storage;
  final Uuid _uuid = const Uuid();

  BudgetService(this._storage);

  // Create new budget
  Future<BudgetResult> createBudget({
    required String userId,
    required String name,
    required String categoryId,
    required int allocatedAmount,
    required DateTime startDate,
    required DateTime endDate,
    String? description,
    int alertThreshold = 80, // Alert when 80% of budget is used
  }) async {
    try {
      final budget = Budget(
        id: _uuid.v4(),
        userId: userId,
        name: name,
        categoryId: categoryId,
        allocatedAmount: allocatedAmount,
        spentAmount: 0,
        startDate: startDate,
        endDate: endDate,
        description: description,
        alertThreshold: alertThreshold,
        isActive: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      await _storage.saveBudget(budget);
      return BudgetResult.success(budget);
    } catch (e) {
      return BudgetResult.failure(
        'Erreur lors de la création: ${e.toString()}',
      );
    }
  }

  // Update budget
  Future<BudgetResult> updateBudget({
    required String budgetId,
    String? name,
    int? allocatedAmount,
    DateTime? startDate,
    DateTime? endDate,
    String? description,
    int? alertThreshold,
    bool? isActive,
  }) async {
    try {
      final existingBudget = _storage.getBudget(budgetId);
      if (existingBudget == null) {
        return BudgetResult.failure('Budget non trouvé');
      }

      final updatedBudget = existingBudget.copyWith(
        name: name ?? existingBudget.name,
        allocatedAmount: allocatedAmount ?? existingBudget.allocatedAmount,
        startDate: startDate ?? existingBudget.startDate,
        endDate: endDate ?? existingBudget.endDate,
        description: description ?? existingBudget.description,
        alertThreshold: alertThreshold ?? existingBudget.alertThreshold,
        isActive: isActive ?? existingBudget.isActive,
        updatedAt: DateTime.now(),
      );

      await _storage.saveBudget(updatedBudget);
      return BudgetResult.success(updatedBudget);
    } catch (e) {
      return BudgetResult.failure(
        'Erreur lors de la mise à jour: ${e.toString()}',
      );
    }
  }

  // Delete budget
  Future<BudgetResult> deleteBudget(String budgetId) async {
    try {
      final budget = _storage.getBudget(budgetId);
      if (budget == null) {
        return BudgetResult.failure('Budget non trouvé');
      }

      await _storage.deleteBudget(budgetId);
      return BudgetResult.success(budget);
    } catch (e) {
      return BudgetResult.failure(
        'Erreur lors de la suppression: ${e.toString()}',
      );
    }
  }

  // Get budgets by user
  Future<List<Budget>> getBudgetsByUser(String userId) async {
    final budgets = _storage.getBudgetsByUserId(userId)
      ..sort((a, b) => b.createdAt.compareTo(a.createdAt));
    return budgets;
  }

  // Get active budgets
  Future<List<Budget>> getActiveBudgets(String userId) async {
    final budgets = await getBudgetsByUser(userId);
    final now = DateTime.now();

    return budgets.where((budget) {
      return budget.isActive &&
          budget.startDate.isBefore(now.add(const Duration(days: 1))) &&
          budget.endDate.isAfter(now.subtract(const Duration(days: 1)));
    }).toList();
  }

  // Update budget spent amounts based on transactions
  Future<void> updateBudgetSpentAmounts(String userId) async {
    final budgets = await getBudgetsByUser(userId);
    final transactions = _storage.getTransactionsByUserId(userId);

    for (final budget in budgets) {
      final budgetTransactions = transactions.where((transaction) {
        return transaction.categoryId == budget.categoryId &&
            transaction.isExpense &&
            transaction.date.isAfter(
              budget.startDate.subtract(const Duration(days: 1)),
            ) &&
            transaction.date.isBefore(
              budget.endDate.add(const Duration(days: 1)),
            );
      });

      final spentAmount = budgetTransactions.fold<int>(
        0,
        (sum, transaction) => sum + transaction.amount,
      );

      if (spentAmount != budget.spentAmount) {
        final updatedBudget = budget.copyWith(
          spentAmount: spentAmount,
          updatedAt: DateTime.now(),
        );
        await _storage.saveBudget(updatedBudget);
      }
    }
  }

  // Get budget by category and date
  Future<Budget?> getBudgetByCategoryAndDate(
    String userId,
    String categoryId,
    DateTime date,
  ) async {
    final budgets = await getBudgetsByUser(userId);

    try {
      return budgets.firstWhere((budget) {
        return budget.categoryId == categoryId &&
            budget.isActive &&
            budget.startDate.isBefore(date.add(const Duration(days: 1))) &&
            budget.endDate.isAfter(date.subtract(const Duration(days: 1)));
      });
    } catch (e) {
      return null;
    }
  }

  // Calculate budget usage percentage
  double calculateBudgetUsage(Budget budget) {
    if (budget.allocatedAmount == 0) return 0.0;
    return (budget.spentAmount / budget.allocatedAmount) * 100;
  }

  // Check if budget is over threshold
  bool isBudgetOverThreshold(Budget budget) {
    final usage = calculateBudgetUsage(budget);
    return usage >= budget.alertThreshold;
  }

  // Check if budget is exceeded
  bool isBudgetExceeded(Budget budget) {
    return budget.spentAmount > budget.allocatedAmount;
  }

  // Get remaining budget amount
  int getRemainingBudgetAmount(Budget budget) {
    return budget.allocatedAmount - budget.spentAmount;
  }

  // Get budgets that need alerts
  Future<List<Budget>> getBudgetsNeedingAlerts(String userId) async {
    final activeBudgets = await getActiveBudgets(userId);
    return activeBudgets.where((budget) {
      return isBudgetOverThreshold(budget) || isBudgetExceeded(budget);
    }).toList();
  }

  // Get budget summary for a period
  Future<BudgetSummary> getBudgetSummary(
    String userId,
    DateTime startDate,
    DateTime endDate,
  ) async {
    final budgets = await getBudgetsByUser(userId);
    final periodBudgets =
        budgets.where((budget) {
          return budget.startDate.isBefore(
                endDate.add(const Duration(days: 1)),
              ) &&
              budget.endDate.isAfter(
                startDate.subtract(const Duration(days: 1)),
              );
        }).toList();

    final totalAllocated = periodBudgets.fold(
      0,
      (sum, budget) => sum + budget.allocatedAmount,
    );

    final totalSpent = periodBudgets.fold(
      0,
      (sum, budget) => sum + budget.spentAmount,
    );

    final budgetsOverThreshold =
        periodBudgets.where(isBudgetOverThreshold).length;
    final budgetsExceeded = periodBudgets.where(isBudgetExceeded).length;

    return BudgetSummary(
      totalBudgets: periodBudgets.length,
      totalAllocated: totalAllocated,
      totalSpent: totalSpent,
      budgetsOverThreshold: budgetsOverThreshold,
      budgetsExceeded: budgetsExceeded,
      averageUsage:
          periodBudgets.isEmpty
              ? 0.0
              : periodBudgets
                      .map(calculateBudgetUsage)
                      .reduce((a, b) => a + b) /
                  periodBudgets.length,
    );
  }

  // Get spending forecast
  int getSpendingForecast(Budget budget) {
    final now = DateTime.now();
    final totalDays = budget.endDate.difference(budget.startDate).inDays;
    final elapsedDays = now.difference(budget.startDate).inDays;

    if (elapsedDays <= 0 || totalDays <= 0) return 0;

    final dailySpending = budget.spentAmount / elapsedDays;
    final remainingDays = budget.endDate.difference(now).inDays;

    return (budget.spentAmount + (dailySpending * remainingDays)).round();
  }

  // Check if spending is on track
  bool isSpendingOnTrack(Budget budget) {
    final forecast = getSpendingForecast(budget);
    return forecast <= budget.allocatedAmount;
  }
}

class BudgetResult {
  final bool success;
  final String? message;
  final Budget? budget;

  BudgetResult._(this.success, this.message, this.budget);

  factory BudgetResult.success(Budget budget) {
    return BudgetResult._(true, null, budget);
  }

  factory BudgetResult.failure(String message) {
    return BudgetResult._(false, message, null);
  }
}

class BudgetSummary {
  final int totalBudgets;
  final int totalAllocated;
  final int totalSpent;
  final int budgetsOverThreshold;
  final int budgetsExceeded;
  final double averageUsage;

  BudgetSummary({
    required this.totalBudgets,
    required this.totalAllocated,
    required this.totalSpent,
    required this.budgetsOverThreshold,
    required this.budgetsExceeded,
    required this.averageUsage,
  });

  int get remainingBudget => totalAllocated - totalSpent;
  double get usagePercentage =>
      totalAllocated == 0 ? 0.0 : (totalSpent / totalAllocated) * 100;
}
