import 'package:flutter/material.dart';
import '../../domain/category.dart';
import '../../domain/subcategory.dart';

class CategoryCard extends StatefulWidget {
  final Category category;
  final List<SubCategory> subCategories;
  final VoidCallback onEditCategory;
  final VoidCallback onDeleteCategory;
  final VoidCallback onAddSubCategory;
  final Function(SubCategory) onEditSubCategory;
  final Function(SubCategory) onDeleteSubCategory;

  const CategoryCard({
    super.key,
    required this.category,
    required this.subCategories,
    required this.onEditCategory,
    required this.onDeleteCategory,
    required this.onAddSubCategory,
    required this.onEditSubCategory,
    required this.onDeleteSubCategory,
  });

  @override
  State<CategoryCard> createState() => _CategoryCardState();
}

class _CategoryCardState extends State<CategoryCard> {
  bool _isExpanded = false;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    // Convertir la couleur hexadécimale en Color
    Color categoryColor = colorScheme.primary;
    if (widget.category.color != null) {
      try {
        categoryColor = Color(
          int.parse(widget.category.color!.replaceFirst('#', '0xFF')),
        );
      } catch (e) {
        // Utiliser la couleur par défaut si la conversion échoue
      }
    }

    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: 2,
      child: Column(
        children: [
          // En-tête de la catégorie
          ListTile(
            leading: CircleAvatar(
              backgroundColor: categoryColor.withValues(alpha: 0.2),
              child: Icon(
                _getIconData(widget.category.iconName),
                color: categoryColor,
                size: 20,
              ),
            ),
            title: Text(
              widget.category.name,
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            subtitle: Text(
              '${widget.subCategories.length} sous-catégorie${widget.subCategories.length > 1 ? 's' : ''}',
              style: theme.textTheme.bodySmall?.copyWith(
                color: Colors.grey[600],
              ),
            ),
            trailing: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Bouton d'expansion
                IconButton(
                  icon: Icon(
                    _isExpanded ? Icons.expand_less : Icons.expand_more,
                    color: Colors.grey[600],
                  ),
                  onPressed: () {
                    setState(() {
                      _isExpanded = !_isExpanded;
                    });
                  },
                ),
                // Menu d'actions
                PopupMenuButton<String>(
                  icon: Icon(Icons.more_vert, color: Colors.grey[600]),
                  onSelected: (value) {
                    switch (value) {
                      case 'edit':
                        widget.onEditCategory();
                        break;
                      case 'delete':
                        widget.onDeleteCategory();
                        break;
                      case 'add_sub':
                        widget.onAddSubCategory();
                        break;
                    }
                  },
                  itemBuilder:
                      (context) => [
                        const PopupMenuItem(
                          value: 'edit',
                          child: Row(
                            children: [
                              Icon(Icons.edit, size: 20),
                              SizedBox(width: 8),
                              Text('Modifier'),
                            ],
                          ),
                        ),
                        const PopupMenuItem(
                          value: 'add_sub',
                          child: Row(
                            children: [
                              Icon(Icons.add, size: 20),
                              SizedBox(width: 8),
                              Text('Ajouter sous-catégorie'),
                            ],
                          ),
                        ),
                        if (widget.category.userId !=
                            null) // Seulement pour les catégories personnalisées
                          const PopupMenuItem(
                            value: 'delete',
                            child: Row(
                              children: [
                                Icon(Icons.delete, size: 20, color: Colors.red),
                                SizedBox(width: 8),
                                Text(
                                  'Supprimer',
                                  style: TextStyle(color: Colors.red),
                                ),
                              ],
                            ),
                          ),
                      ],
                ),
              ],
            ),
          ),

          // Liste des sous-catégories (si étendue)
          if (_isExpanded && widget.subCategories.isNotEmpty)
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Divider(color: Colors.grey[300]),
                  const SizedBox(height: 8),
                  Text(
                    'Sous-catégories:',
                    style: theme.textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.w500,
                      color: Colors.grey[700],
                    ),
                  ),
                  const SizedBox(height: 8),
                  ...widget.subCategories.map(
                    (subCategory) =>
                        _buildSubCategoryTile(subCategory, categoryColor),
                  ),
                ],
              ),
            ),

          // Message si aucune sous-catégorie
          if (_isExpanded && widget.subCategories.isEmpty)
            Container(
              padding: const EdgeInsets.all(16),
              child: Column(
                children: [
                  Divider(color: Colors.grey[300]),
                  const SizedBox(height: 8),
                  Text(
                    'Aucune sous-catégorie',
                    style: theme.textTheme.bodyMedium?.copyWith(
                      color: Colors.grey[600],
                      fontStyle: FontStyle.italic,
                    ),
                  ),
                  const SizedBox(height: 8),
                  TextButton.icon(
                    onPressed: widget.onAddSubCategory,
                    icon: const Icon(Icons.add, size: 16),
                    label: const Text('Ajouter une sous-catégorie'),
                    style: TextButton.styleFrom(foregroundColor: categoryColor),
                  ),
                ],
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildSubCategoryTile(SubCategory subCategory, Color categoryColor) {
    return Container(
      margin: const EdgeInsets.only(bottom: 4),
      child: ListTile(
        dense: true,
        contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 2),
        leading: Icon(
          _getIconData(subCategory.iconName),
          size: 18,
          color: categoryColor.withValues(alpha: 0.7),
        ),
        title: Text(
          subCategory.name,
          style: Theme.of(context).textTheme.bodyMedium,
        ),
        trailing: PopupMenuButton<String>(
          icon: Icon(Icons.more_horiz, size: 18, color: Colors.grey[600]),
          onSelected: (value) {
            switch (value) {
              case 'edit':
                widget.onEditSubCategory(subCategory);
                break;
              case 'delete':
                widget.onDeleteSubCategory(subCategory);
                break;
            }
          },
          itemBuilder:
              (context) => [
                const PopupMenuItem(
                  value: 'edit',
                  child: Row(
                    children: [
                      Icon(Icons.edit, size: 16),
                      SizedBox(width: 8),
                      Text('Modifier'),
                    ],
                  ),
                ),
                if (subCategory.userId !=
                    null) // Seulement pour les sous-catégories personnalisées
                  const PopupMenuItem(
                    value: 'delete',
                    child: Row(
                      children: [
                        Icon(Icons.delete, size: 16, color: Colors.red),
                        SizedBox(width: 8),
                        Text('Supprimer', style: TextStyle(color: Colors.red)),
                      ],
                    ),
                  ),
              ],
        ),
      ),
    );
  }

  IconData _getIconData(String? iconName) {
    if (iconName == null) return Icons.category;

    // Mapping des noms d'icônes vers les IconData
    final iconMap = {
      'home': Icons.home,
      'restaurant': Icons.restaurant,
      'directions_car': Icons.directions_car,
      'school': Icons.school,
      'local_hospital': Icons.local_hospital,
      'checkroom': Icons.checkroom,
      'phone': Icons.phone,
      'sports_esports': Icons.sports_esports,
      'celebration': Icons.celebration,
      'volunteer_activism': Icons.volunteer_activism,
      'savings': Icons.savings,
      'work': Icons.work,
      'store': Icons.store,
      'card_giftcard': Icons.card_giftcard,
      'home_work': Icons.home_work,
      'electrical_services': Icons.electrical_services,
      'water_drop': Icons.water_drop,
      'build': Icons.build,
      'local_grocery_store': Icons.local_grocery_store,
      'shopping_cart': Icons.shopping_cart,
      'local_bar': Icons.local_bar,
      'local_gas_station': Icons.local_gas_station,
      'directions_bus': Icons.directions_bus,
      'local_taxi': Icons.local_taxi,
      'car_repair': Icons.car_repair,
      'edit': Icons.edit,
      'menu_book': Icons.menu_book,
      'person': Icons.person,
      'medication': Icons.medication,
      'medical_services': Icons.medical_services,
      'dentistry':
          Icons.medical_services, // Pas d'icône dentistry dans Material
      'health_and_safety': Icons.health_and_safety,
      'phone_android': Icons.phone_android,
      'wifi': Icons.wifi,
      'mail': Icons.mail,
      'category': Icons.category,
    };

    return iconMap[iconName] ?? Icons.category;
  }
}
