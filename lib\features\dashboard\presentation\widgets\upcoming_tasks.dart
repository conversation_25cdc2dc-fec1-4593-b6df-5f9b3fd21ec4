import 'package:flutter/material.dart';
import 'package:gestion_budget_famille_zoro/core/theme/app_theme.dart';

class UpcomingTasks extends StatelessWidget {
  const UpcomingTasks({super.key});

  @override
  Widget build(BuildContext context) {
    // Données fictives pour démonstration
    final List<Map<String, dynamic>> tasks = [
      {
        'id': '1',
        'name': '<PERSON><PERSON> le loyer',
        'amount': 15000000, // 150,000 FCFA en centimes
        'dueDate': DateTime.now().add(const Duration(days: 5)),
      },
      {
        'id': '2',
        'name': 'Facture internet',
        'amount': 2500000, // 25,000 FCFA en centimes
        'dueDate': DateTime.now().add(const Duration(days: 8)),
      },
    ];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            const Text(
              'Paiements à venir',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            TextButton(
              onPressed: () {
                // context.pushWithAnimation(
                //   SomeScreen(),
                //   type: PageTransitionType.scale,
                // );
              },
              child: const Text('Voir tout'),
            ),
          ],
        ),
        const SizedBox(height: 8),
        ...tasks.map((task) => _TaskItem(task: task)),
      ],
    );
  }
}

class _TaskItem extends StatelessWidget {
  final Map<String, dynamic> task;

  const _TaskItem({required this.task});

  @override
  Widget build(BuildContext context) {
    // Fonction pour formater le montant
    String formatAmount(int amount) {
      return '${(amount / 100).toStringAsFixed(0)} FCFA';
    }

    // Fonction pour formater la date
    String formatDate(DateTime date) {
      return '${date.day}/${date.month}/${date.year}';
    }

    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        leading: const CircleAvatar(
          backgroundColor: Colors.orange,
          child: Icon(Icons.calendar_today, color: Colors.white),
        ),
        title: Text(task['name']),
        subtitle: Text('Échéance: ${formatDate(task['dueDate'])}'),
        trailing: Text(
          formatAmount(task['amount']),
          style: const TextStyle(
            fontWeight: FontWeight.bold,
            color: AppTheme.alertColor,
          ),
        ),
      ),
    );
  }
}
