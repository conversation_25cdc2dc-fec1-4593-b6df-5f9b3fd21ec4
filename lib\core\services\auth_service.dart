import 'package:local_auth/local_auth.dart';
import 'package:uuid/uuid.dart';
import '../../features/auth/domain/user.dart';
import 'storage_service.dart';

class AuthService {
  final StorageService _storage;
  final LocalAuthentication _localAuth = LocalAuthentication();
  final Uuid _uuid = const Uuid();

  AuthService(this._storage);

  // Current user
  User? _currentUser;
  User? get currentUser => _currentUser;

  // Initialize auth service
  Future<void> init() async {
    final userId = await _storage.getCurrentUserId();
    if (userId != null) {
      _currentUser = _storage.getUser(userId);
    }
  }

  // Login with email and password
  Future<AuthResult> login(String email, String password) async {
    try {
      // Find user by email
      final users = _storage.getAllUsers();
      final user = users.firstWhere(
        (u) => u.email.toLowerCase() == email.toLowerCase(),
        orElse: () => throw Exception('User not found'),
      );

      // Verify password (in real app, use proper hashing)
      if (user.password != password) {
        return AuthResult.failure('Mot de passe incorrect');
      }

      // Check if biometric is enabled and available
      final biometricEnabled = await _storage.getBiometricEnabled();
      if (biometricEnabled) {
        final biometricResult = await authenticateWithBiometric();
        if (!biometricResult.success) {
          return biometricResult;
        }
      }

      _currentUser = user;
      await _storage.saveCurrentUserId(user.id);

      return AuthResult.success(user);
    } catch (e) {
      return AuthResult.failure('Erreur de connexion: ${e.toString()}');
    }
  }

  // Login with access code
  Future<AuthResult> loginWithCode(String code) async {
    try {
      // Verify the access code
      if (code != '03117455') {
        return AuthResult.failure('Code d\'accès incorrect');
      }

      // Find the first admin user to login with
      final users = _storage.getAllUsers();
      final adminUser = users.firstWhere(
        (u) => u.role.toLowerCase() == 'admin',
        orElse:
            () => throw Exception('Aucun utilisateur administrateur trouvé'),
      );

      _currentUser = adminUser;
      await _storage.saveCurrentUserId(adminUser.id);

      return AuthResult.success(adminUser);
    } catch (e) {
      return AuthResult.failure(
        'Erreur de connexion par code: ${e.toString()}',
      );
    }
  }

  // Register new user
  Future<AuthResult> register({
    required String email,
    required String password,
    required String firstName,
    required String lastName,
  }) async {
    try {
      // Check if user already exists
      final users = _storage.getAllUsers();
      final existingUser = users.where(
        (u) => u.email.toLowerCase() == email.toLowerCase(),
      );

      if (existingUser.isNotEmpty) {
        return AuthResult.failure('Un utilisateur avec cet email existe déjà');
      }

      // Create new user
      final user = User(
        id: _uuid.v4(),
        email: email,
        firstName: firstName,
        lastName: lastName,
        password: password, // In real app, hash this
        profilePicture: null,
        householdId: _uuid.v4(), // Create new household
        role: 'admin',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      await _storage.saveUser(user);
      _currentUser = user;
      await _storage.saveCurrentUserId(user.id);

      return AuthResult.success(user);
    } catch (e) {
      return AuthResult.failure('Erreur d\'inscription: ${e.toString()}');
    }
  }

  // Logout
  Future<void> logout() async {
    _currentUser = null;
    await _storage.saveCurrentUserId(null);
  }

  // Check if user is logged in
  bool get isLoggedIn => _currentUser != null;

  // Biometric authentication
  Future<bool> isBiometricAvailable() async {
    try {
      final isAvailable = await _localAuth.canCheckBiometrics;
      final isDeviceSupported = await _localAuth.isDeviceSupported();
      return isAvailable && isDeviceSupported;
    } catch (e) {
      return false;
    }
  }

  Future<List<BiometricType>> getAvailableBiometrics() async {
    try {
      return await _localAuth.getAvailableBiometrics();
    } catch (e) {
      return [];
    }
  }

  Future<AuthResult> authenticateWithBiometric() async {
    try {
      final isAvailable = await isBiometricAvailable();
      if (!isAvailable) {
        return AuthResult.failure(
          'Authentification biométrique non disponible',
        );
      }

      final didAuthenticate = await _localAuth.authenticate(
        localizedReason:
            'Veuillez vous authentifier pour accéder à l\'application',
        options: const AuthenticationOptions(
          biometricOnly: true,
          stickyAuth: true,
        ),
      );

      if (didAuthenticate) {
        return AuthResult.success(null);
      } else {
        return AuthResult.failure('Authentification biométrique échouée');
      }
    } catch (e) {
      return AuthResult.failure('Erreur d\'authentification: ${e.toString()}');
    }
  }

  // Alias for authenticateWithBiometric to match usage in login_screen
  Future<AuthResult> authenticateWithBiometrics() async {
    return authenticateWithBiometric();
  }

  // Enable/disable biometric authentication
  Future<bool> enableBiometric() async {
    final isAvailable = await isBiometricAvailable();
    if (!isAvailable) return false;

    final result = await authenticateWithBiometric();
    if (result.success) {
      await _storage.saveBiometricEnabled(true);
      return true;
    }
    return false;
  }

  Future<void> disableBiometric() async {
    await _storage.saveBiometricEnabled(false);
  }

  // Update user profile
  Future<AuthResult> updateProfile({
    String? firstName,
    String? lastName,
    String? profilePicture,
  }) async {
    try {
      if (_currentUser == null) {
        return AuthResult.failure('Utilisateur non connecté');
      }

      final updatedUser = _currentUser!.copyWith(
        firstName: firstName ?? _currentUser!.firstName,
        lastName: lastName ?? _currentUser!.lastName,
        profilePicture: profilePicture ?? _currentUser!.profilePicture,
        updatedAt: DateTime.now(),
      );

      await _storage.saveUser(updatedUser);
      _currentUser = updatedUser;

      return AuthResult.success(updatedUser);
    } catch (e) {
      return AuthResult.failure('Erreur de mise à jour: ${e.toString()}');
    }
  }

  // Change password
  Future<AuthResult> changePassword(
    String currentPassword,
    String newPassword,
  ) async {
    try {
      if (_currentUser == null) {
        return AuthResult.failure('Utilisateur non connecté');
      }

      if (_currentUser!.password != currentPassword) {
        return AuthResult.failure('Mot de passe actuel incorrect');
      }

      final updatedUser = _currentUser!.copyWith(
        password: newPassword,
        updatedAt: DateTime.now(),
      );

      await _storage.saveUser(updatedUser);
      _currentUser = updatedUser;

      return AuthResult.success(updatedUser);
    } catch (e) {
      return AuthResult.failure(
        'Erreur de changement de mot de passe: ${e.toString()}',
      );
    }
  }
}

class AuthResult {
  final bool success;
  final String? message;
  final User? user;

  AuthResult._(this.success, this.message, this.user);

  factory AuthResult.success(User? user) {
    return AuthResult._(true, null, user);
  }

  factory AuthResult.failure(String message) {
    return AuthResult._(false, message, null);
  }
}
