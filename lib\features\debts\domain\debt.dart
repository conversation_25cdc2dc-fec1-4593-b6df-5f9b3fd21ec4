import 'package:hive/hive.dart';

part 'debt.g.dart';

@HiveType(typeId: 7)
class Debt {
  @HiveField(0)
  final String id;
  
  @HiveField(1)
  final String userId;
  
  @HiveField(2)
  final String creditorName;
  
  @HiveField(3)
  final int totalAmount; // Montant en centimes
  
  @HiveField(4)
  final String reason;
  
  @HiveField(5)
  final DebtType type;
  
  @HiveField(6)
  final DateTime loanDate;
  
  @HiveField(7)
  final DateTime? dueDate;
  
  @HiveField(8)
  final RepaymentType repaymentType;
  
  @HiveField(9)
  final int? monthlyAmount; // Pour remboursement échelonné
  
  @HiveField(10)
  final String? attachmentPath;
  
  @HiveField(11)
  final DebtStatus status;
  
  @HiveField(12)
  final DateTime createdAt;
  
  @HiveField(13)
  final DateTime updatedAt;
  
  @HiveField(14)
  final String? notes;

  Debt({
    required this.id,
    required this.userId,
    required this.creditorName,
    required this.totalAmount,
    required this.reason,
    required this.type,
    required this.loanDate,
    this.dueDate,
    required this.repaymentType,
    this.monthlyAmount,
    this.attachmentPath,
    required this.status,
    required this.createdAt,
    required this.updatedAt,
    this.notes,
  });

  // Getters calculés
  String get formattedTotalAmount {
    final amountInFcfa = totalAmount / 100;
    return '${amountInFcfa.toStringAsFixed(0)} FCFA';
  }

  String get formattedMonthlyAmount {
    if (monthlyAmount == null) return '';
    final amountInFcfa = monthlyAmount! / 100;
    return '${amountInFcfa.toStringAsFixed(0)} FCFA';
  }

  bool get isOverdue {
    if (dueDate == null || status == DebtStatus.paid) return false;
    return DateTime.now().isAfter(dueDate!);
  }

  int get daysUntilDue {
    if (dueDate == null) return 0;
    return dueDate!.difference(DateTime.now()).inDays;
  }

  Debt copyWith({
    String? id,
    String? userId,
    String? creditorName,
    int? totalAmount,
    String? reason,
    DebtType? type,
    DateTime? loanDate,
    DateTime? dueDate,
    RepaymentType? repaymentType,
    int? monthlyAmount,
    String? attachmentPath,
    DebtStatus? status,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? notes,
  }) {
    return Debt(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      creditorName: creditorName ?? this.creditorName,
      totalAmount: totalAmount ?? this.totalAmount,
      reason: reason ?? this.reason,
      type: type ?? this.type,
      loanDate: loanDate ?? this.loanDate,
      dueDate: dueDate ?? this.dueDate,
      repaymentType: repaymentType ?? this.repaymentType,
      monthlyAmount: monthlyAmount ?? this.monthlyAmount,
      attachmentPath: attachmentPath ?? this.attachmentPath,
      status: status ?? this.status,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      notes: notes ?? this.notes,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'userId': userId,
      'creditorName': creditorName,
      'totalAmount': totalAmount,
      'reason': reason,
      'type': type.name,
      'loanDate': loanDate.toIso8601String(),
      'dueDate': dueDate?.toIso8601String(),
      'repaymentType': repaymentType.name,
      'monthlyAmount': monthlyAmount,
      'attachmentPath': attachmentPath,
      'status': status.name,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'notes': notes,
    };
  }

  factory Debt.fromJson(Map<String, dynamic> json) {
    return Debt(
      id: json['id'],
      userId: json['userId'],
      creditorName: json['creditorName'],
      totalAmount: json['totalAmount'],
      reason: json['reason'],
      type: DebtType.values.firstWhere((e) => e.name == json['type']),
      loanDate: DateTime.parse(json['loanDate']),
      dueDate: json['dueDate'] != null ? DateTime.parse(json['dueDate']) : null,
      repaymentType: RepaymentType.values.firstWhere((e) => e.name == json['repaymentType']),
      monthlyAmount: json['monthlyAmount'],
      attachmentPath: json['attachmentPath'],
      status: DebtStatus.values.firstWhere((e) => e.name == json['status']),
      createdAt: DateTime.parse(json['createdAt']),
      updatedAt: DateTime.parse(json['updatedAt']),
      notes: json['notes'],
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Debt && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'Debt(id: $id, creditor: $creditorName, amount: $formattedTotalAmount, status: $status)';
  }
}

@HiveType(typeId: 8)
enum DebtType {
  @HiveField(0)
  personal, // Dette personnelle
  
  @HiveField(1)
  institutional, // Dette institutionnelle (banque, etc.)
  
  @HiveField(2)
  commercial, // Dette commerciale
}

@HiveType(typeId: 9)
enum RepaymentType {
  @HiveField(0)
  single, // Remboursement unique
  
  @HiveField(1)
  monthly, // Mensuel
  
  @HiveField(2)
  weekly, // Hebdomadaire
  
  @HiveField(3)
  biweekly, // Bi-hebdomadaire
  
  @HiveField(4)
  quarterly, // Trimestriel
}

@HiveType(typeId: 10)
enum DebtStatus {
  @HiveField(0)
  active, // Dette active
  
  @HiveField(1)
  paid, // Dette remboursée
  
  @HiveField(2)
  overdue, // Dette en retard
  
  @HiveField(3)
  suspended, // Dette suspendue
}

extension DebtTypeExtension on DebtType {
  String get displayName {
    switch (this) {
      case DebtType.personal:
        return 'Personnel';
      case DebtType.institutional:
        return 'Institutionnel';
      case DebtType.commercial:
        return 'Commercial';
    }
  }

  String get emoji {
    switch (this) {
      case DebtType.personal:
        return '👤';
      case DebtType.institutional:
        return '🏦';
      case DebtType.commercial:
        return '🏪';
    }
  }
}

extension RepaymentTypeExtension on RepaymentType {
  String get displayName {
    switch (this) {
      case RepaymentType.single:
        return 'Unique';
      case RepaymentType.monthly:
        return 'Mensuel';
      case RepaymentType.weekly:
        return 'Hebdomadaire';
      case RepaymentType.biweekly:
        return 'Bi-hebdomadaire';
      case RepaymentType.quarterly:
        return 'Trimestriel';
    }
  }
}

extension DebtStatusExtension on DebtStatus {
  String get displayName {
    switch (this) {
      case DebtStatus.active:
        return 'Active';
      case DebtStatus.paid:
        return 'Remboursée';
      case DebtStatus.overdue:
        return 'En retard';
      case DebtStatus.suspended:
        return 'Suspendue';
    }
  }

  String get emoji {
    switch (this) {
      case DebtStatus.active:
        return '🔄';
      case DebtStatus.paid:
        return '✅';
      case DebtStatus.overdue:
        return '⚠️';
      case DebtStatus.suspended:
        return '⏸️';
    }
  }
}
