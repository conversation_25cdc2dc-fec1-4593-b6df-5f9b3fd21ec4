import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:timezone/timezone.dart' as tz;
import 'package:flutter/foundation.dart';
import '../../features/budgets/domain/budget.dart';
import '../../features/transactions/domain/transaction.dart';

class NotificationService {
  static final NotificationService _instance = NotificationService._internal();
  factory NotificationService() => _instance;
  NotificationService._internal();

  final FlutterLocalNotificationsPlugin _notifications =
      FlutterLocalNotificationsPlugin();
  bool _isInitialized = false;

  // Initialize notifications
  Future<void> init() async {
    if (_isInitialized) return;

    const androidSettings = AndroidInitializationSettings(
      '@mipmap/ic_launcher',
    );
    const iosSettings = DarwinInitializationSettings(
      requestAlertPermission: true,
      requestBadgePermission: true,
      requestSoundPermission: true,
    );

    const initSettings = InitializationSettings(
      android: androidSettings,
      iOS: iosSettings,
    );

    await _notifications.initialize(
      initSettings,
      onDidReceiveNotificationResponse: _onNotificationTapped,
    );

    _isInitialized = true;
  }

  // Handle notification tap
  void _onNotificationTapped(NotificationResponse response) {
    debugPrint('Notification tapped: ${response.payload}');
    // Handle navigation based on payload
  }

  // Request permissions
  Future<bool> requestPermissions() async {
    if (!_isInitialized) await init();

    final androidPlugin =
        _notifications
            .resolvePlatformSpecificImplementation<
              AndroidFlutterLocalNotificationsPlugin
            >();
    final iosPlugin =
        _notifications
            .resolvePlatformSpecificImplementation<
              IOSFlutterLocalNotificationsPlugin
            >();

    bool granted = true;

    if (androidPlugin != null) {
      granted = await androidPlugin.requestNotificationsPermission() ?? false;
    }

    if (iosPlugin != null) {
      granted =
          await iosPlugin.requestPermissions(
            alert: true,
            badge: true,
            sound: true,
          ) ??
          false;
    }

    return granted;
  }

  // Show immediate notification
  Future<void> showNotification({
    required int id,
    required String title,
    required String body,
    String? payload,
    NotificationPriority priority = NotificationPriority.defaultPriority,
  }) async {
    if (!_isInitialized) await init();

    final androidDetails = AndroidNotificationDetails(
      'budget_alerts',
      'Alertes Budget',
      channelDescription: 'Notifications pour les alertes de budget',
      importance: _getAndroidImportance(priority),
      priority: _getAndroidPriority(priority),
      showWhen: true,
    );

    const iosDetails = DarwinNotificationDetails(
      presentAlert: true,
      presentBadge: true,
      presentSound: true,
    );

    final details = NotificationDetails(
      android: androidDetails,
      iOS: iosDetails,
    );

    await _notifications.show(id, title, body, details, payload: payload);
  }

  // Schedule notification
  Future<void> scheduleNotification({
    required int id,
    required String title,
    required String body,
    required DateTime scheduledDate,
    String? payload,
    NotificationPriority priority = NotificationPriority.defaultPriority,
  }) async {
    if (!_isInitialized) await init();

    final androidDetails = AndroidNotificationDetails(
      'budget_reminders',
      'Rappels Budget',
      channelDescription: 'Rappels pour la gestion du budget',
      importance: _getAndroidImportance(priority),
      priority: _getAndroidPriority(priority),
      showWhen: true,
    );

    const iosDetails = DarwinNotificationDetails(
      presentAlert: true,
      presentBadge: true,
      presentSound: true,
    );

    final details = NotificationDetails(
      android: androidDetails,
      iOS: iosDetails,
    );

    await _notifications.zonedSchedule(
      id,
      title,
      body,
      tz.TZDateTime.from(scheduledDate, tz.local),
      details,
      payload: payload,
      androidScheduleMode: AndroidScheduleMode.exactAllowWhileIdle,
      uiLocalNotificationDateInterpretation:
          UILocalNotificationDateInterpretation.absoluteTime,
    );
  }

  // Budget alert notifications
  Future<void> showBudgetAlert(Budget budget) async {
    final usage = (budget.spentAmount / budget.allocatedAmount * 100).round();

    String title;
    String body;
    NotificationPriority priority;

    if (budget.spentAmount > budget.allocatedAmount) {
      title = '🚨 Budget dépassé!';
      body = 'Votre budget "${budget.name}" a été dépassé de ${usage - 100}%';
      priority = NotificationPriority.high;
    } else if (usage >= budget.alertThreshold) {
      title = '⚠️ Alerte budget';
      body = 'Vous avez utilisé $usage% de votre budget "${budget.name}"';
      priority = NotificationPriority.defaultPriority;
    } else {
      return; // No alert needed
    }

    await showNotification(
      id: budget.id.hashCode,
      title: title,
      body: body,
      payload: 'budget:${budget.id}',
      priority: priority,
    );
  }

  // Transaction confirmation notification
  Future<void> showTransactionConfirmation(Transaction transaction) async {
    final type = transaction.isExpense ? 'Dépense' : 'Revenu';
    final amount = (transaction.amount / 100).toStringAsFixed(2);

    await showNotification(
      id: transaction.id.hashCode,
      title: '$type enregistrée',
      body: '${transaction.name}: $amount FCFA',
      payload: 'transaction:${transaction.id}',
    );
  }

  // Monthly budget reminder
  Future<void> scheduleMonthlyBudgetReminder() async {
    final now = DateTime.now();
    final nextMonth = DateTime(now.year, now.month + 1, 1);

    await scheduleNotification(
      id: 'monthly_reminder'.hashCode,
      title: '📊 Rappel mensuel',
      body: 'N\'oubliez pas de réviser vos budgets pour le mois prochain',
      scheduledDate: nextMonth,
      payload: 'monthly_reminder',
    );
  }

  // Weekly spending summary
  Future<void> scheduleWeeklySpendingSummary() async {
    final now = DateTime.now();
    final nextSunday = now.add(Duration(days: 7 - now.weekday));
    final summaryTime = DateTime(
      nextSunday.year,
      nextSunday.month,
      nextSunday.day,
      18,
      0,
    );

    await scheduleNotification(
      id: 'weekly_summary'.hashCode,
      title: '📈 Résumé hebdomadaire',
      body: 'Consultez votre résumé de dépenses de la semaine',
      scheduledDate: summaryTime,
      payload: 'weekly_summary',
    );
  }

  // Bill reminder
  Future<void> scheduleBillReminder({
    required String billName,
    required DateTime dueDate,
    required int amount,
    int daysBefore = 3,
  }) async {
    final reminderDate = dueDate.subtract(Duration(days: daysBefore));
    final amountFormatted = (amount / 100).toStringAsFixed(2);

    await scheduleNotification(
      id: billName.hashCode,
      title: '💳 Rappel de facture',
      body: '$billName: $amountFormatted FCFA à payer dans $daysBefore jours',
      scheduledDate: reminderDate,
      payload: 'bill_reminder:$billName',
      priority: NotificationPriority.high,
    );
  }

  // Savings goal reminder
  Future<void> showSavingsGoalReminder({
    required String goalName,
    required int targetAmount,
    required int currentAmount,
  }) async {
    final remaining = targetAmount - currentAmount;
    final remainingFormatted = (remaining / 100).toStringAsFixed(2);

    await showNotification(
      id: goalName.hashCode,
      title: '🎯 Objectif d\'épargne',
      body: 'Il vous reste $remainingFormatted FCFA pour atteindre "$goalName"',
      payload: 'savings_goal:$goalName',
    );
  }

  // Cancel notification
  Future<void> cancelNotification(int id) async {
    await _notifications.cancel(id);
  }

  // Cancel all notifications
  Future<void> cancelAllNotifications() async {
    await _notifications.cancelAll();
  }

  // Get pending notifications
  Future<List<PendingNotificationRequest>> getPendingNotifications() async {
    return await _notifications.pendingNotificationRequests();
  }

  // Helper methods
  Importance _getAndroidImportance(NotificationPriority priority) {
    switch (priority) {
      case NotificationPriority.low:
        return Importance.low;
      case NotificationPriority.defaultPriority:
        return Importance.defaultImportance;
      case NotificationPriority.high:
        return Importance.high;
      case NotificationPriority.max:
        return Importance.max;
    }
  }

  Priority _getAndroidPriority(NotificationPriority priority) {
    switch (priority) {
      case NotificationPriority.low:
        return Priority.low;
      case NotificationPriority.defaultPriority:
        return Priority.defaultPriority;
      case NotificationPriority.high:
        return Priority.high;
      case NotificationPriority.max:
        return Priority.max;
    }
  }
}

enum NotificationPriority { low, defaultPriority, high, max }
