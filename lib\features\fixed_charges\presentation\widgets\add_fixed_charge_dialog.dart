import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../domain/fixed_charge.dart';
import '../../providers/fixed_charge_providers.dart';
import '../../../../core/providers/app_providers.dart';

class AddFixedChargeDialog extends ConsumerStatefulWidget {
  final VoidCallback? onChargeAdded;

  const AddFixedChargeDialog({
    super.key,
    this.onChargeAdded,
  });

  @override
  ConsumerState<AddFixedChargeDialog> createState() => _AddFixedChargeDialogState();
}

class _AddFixedChargeDialogState extends ConsumerState<AddFixedChargeDialog> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _amountController = TextEditingController();
  final _descriptionController = TextEditingController();

  ChargeType _selectedType = ChargeType.housing;
  ChargeRecurrence _selectedRecurrence = ChargeRecurrence.monthly;
  int _dayOfMonth = 1;
  bool _reminderEnabled = true;
  int _reminderDaysBefore = 2;
  bool _isLoading = false;

  @override
  void dispose() {
    _nameController.dispose();
    _amountController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Dialog(
      child: Container(
        constraints: const BoxConstraints(maxWidth: 500, maxHeight: 600),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // En-tête
            Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: theme.colorScheme.primary,
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(12),
                  topRight: Radius.circular(12),
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.add_task,
                    color: theme.colorScheme.onPrimary,
                    size: 24,
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      'Nouvelle Charge Fixe',
                      style: theme.textTheme.titleLarge?.copyWith(
                        color: theme.colorScheme.onPrimary,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  IconButton(
                    onPressed: () => Navigator.of(context).pop(),
                    icon: Icon(
                      Icons.close,
                      color: theme.colorScheme.onPrimary,
                    ),
                  ),
                ],
              ),
            ),

            // Contenu
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(20),
                child: Form(
                  key: _formKey,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Nom de la charge
                      TextFormField(
                        controller: _nameController,
                        decoration: const InputDecoration(
                          labelText: 'Nom de la charge *',
                          hintText: 'Ex: Loyer, Électricité...',
                          prefixIcon: Icon(Icons.label),
                          border: OutlineInputBorder(),
                        ),
                        validator: (value) {
                          if (value == null || value.trim().isEmpty) {
                            return 'Le nom de la charge est requis';
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: 16),

                      // Montant
                      TextFormField(
                        controller: _amountController,
                        decoration: const InputDecoration(
                          labelText: 'Montant (FCFA) *',
                          hintText: 'Ex: 50000',
                          prefixIcon: Icon(Icons.attach_money),
                          border: OutlineInputBorder(),
                        ),
                        keyboardType: TextInputType.number,
                        inputFormatters: [
                          FilteringTextInputFormatter.digitsOnly,
                        ],
                        validator: (value) {
                          if (value == null || value.trim().isEmpty) {
                            return 'Le montant est requis';
                          }
                          final amount = int.tryParse(value);
                          if (amount == null || amount <= 0) {
                            return 'Veuillez entrer un montant valide';
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: 16),

                      // Type de charge
                      DropdownButtonFormField<ChargeType>(
                        value: _selectedType,
                        decoration: const InputDecoration(
                          labelText: 'Type de charge',
                          prefixIcon: Icon(Icons.category),
                          border: OutlineInputBorder(),
                        ),
                        items: ChargeType.values.map((type) {
                          return DropdownMenuItem(
                            value: type,
                            child: Row(
                              children: [
                                Text(type.emoji),
                                const SizedBox(width: 8),
                                Text(type.displayName),
                              ],
                            ),
                          );
                        }).toList(),
                        onChanged: (value) {
                          if (value != null) {
                            setState(() {
                              _selectedType = value;
                            });
                          }
                        },
                      ),
                      const SizedBox(height: 16),

                      // Récurrence
                      DropdownButtonFormField<ChargeRecurrence>(
                        value: _selectedRecurrence,
                        decoration: const InputDecoration(
                          labelText: 'Récurrence',
                          prefixIcon: Icon(Icons.repeat),
                          border: OutlineInputBorder(),
                        ),
                        items: ChargeRecurrence.values.map((recurrence) {
                          return DropdownMenuItem(
                            value: recurrence,
                            child: Row(
                              children: [
                                Text(recurrence.emoji),
                                const SizedBox(width: 8),
                                Text(recurrence.displayName),
                              ],
                            ),
                          );
                        }).toList(),
                        onChanged: (value) {
                          if (value != null) {
                            setState(() {
                              _selectedRecurrence = value;
                            });
                          }
                        },
                      ),
                      const SizedBox(height: 16),

                      // Jour de prélèvement
                      DropdownButtonFormField<int>(
                        value: _dayOfMonth,
                        decoration: InputDecoration(
                          labelText: _selectedRecurrence == ChargeRecurrence.weekly 
                              ? 'Jour de la semaine'
                              : 'Jour du mois',
                          prefixIcon: const Icon(Icons.calendar_today),
                          border: const OutlineInputBorder(),
                        ),
                        items: _getDayItems(),
                        onChanged: (value) {
                          if (value != null) {
                            setState(() {
                              _dayOfMonth = value;
                            });
                          }
                        },
                      ),
                      const SizedBox(height: 16),

                      // Rappel
                      SwitchListTile(
                        title: const Text('Activer les rappels'),
                        subtitle: Text(
                          _reminderEnabled 
                              ? 'Rappel $_reminderDaysBefore jours avant l\'échéance'
                              : 'Aucun rappel'
                        ),
                        value: _reminderEnabled,
                        onChanged: (value) {
                          setState(() {
                            _reminderEnabled = value;
                          });
                        },
                      ),

                      if (_reminderEnabled) ...[
                        const SizedBox(height: 8),
                        DropdownButtonFormField<int>(
                          value: _reminderDaysBefore,
                          decoration: const InputDecoration(
                            labelText: 'Rappel (jours avant)',
                            prefixIcon: Icon(Icons.notifications),
                            border: OutlineInputBorder(),
                          ),
                          items: [1, 2, 3, 5, 7].map((days) {
                            return DropdownMenuItem(
                              value: days,
                              child: Text('$days jour${days > 1 ? 's' : ''} avant'),
                            );
                          }).toList(),
                          onChanged: (value) {
                            if (value != null) {
                              setState(() {
                                _reminderDaysBefore = value;
                              });
                            }
                          },
                        ),
                      ],
                      const SizedBox(height: 16),

                      // Description
                      TextFormField(
                        controller: _descriptionController,
                        decoration: const InputDecoration(
                          labelText: 'Description (optionnel)',
                          hintText: 'Informations supplémentaires...',
                          prefixIcon: Icon(Icons.note),
                          border: OutlineInputBorder(),
                        ),
                        maxLines: 3,
                      ),
                    ],
                  ),
                ),
              ),
            ),

            // Boutons
            Container(
              padding: const EdgeInsets.all(20),
              child: Row(
                children: [
                  Expanded(
                    child: OutlinedButton(
                      onPressed: _isLoading ? null : () => Navigator.of(context).pop(),
                      child: const Text('Annuler'),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: ElevatedButton(
                      onPressed: _isLoading ? null : _saveCharge,
                      child: _isLoading
                          ? const SizedBox(
                              height: 20,
                              width: 20,
                              child: CircularProgressIndicator(strokeWidth: 2),
                            )
                          : const Text('Enregistrer'),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  List<DropdownMenuItem<int>> _getDayItems() {
    if (_selectedRecurrence == ChargeRecurrence.weekly) {
      return [
        const DropdownMenuItem(value: 1, child: Text('Lundi')),
        const DropdownMenuItem(value: 2, child: Text('Mardi')),
        const DropdownMenuItem(value: 3, child: Text('Mercredi')),
        const DropdownMenuItem(value: 4, child: Text('Jeudi')),
        const DropdownMenuItem(value: 5, child: Text('Vendredi')),
        const DropdownMenuItem(value: 6, child: Text('Samedi')),
        const DropdownMenuItem(value: 7, child: Text('Dimanche')),
      ];
    } else {
      return List.generate(28, (index) {
        final day = index + 1;
        return DropdownMenuItem(
          value: day,
          child: Text('Le $day'),
        );
      });
    }
  }

  Future<void> _saveCharge() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final chargeService = ref.read(fixedChargeServiceProvider);
      final authService = ref.read(authServiceProvider);
      final userId = authService.currentUser?.id ?? 'default';

      final amount = int.parse(_amountController.text) * 100; // Convertir en centimes

      await chargeService.createFixedCharge(
        userId: userId,
        name: _nameController.text.trim(),
        amount: amount,
        dayOfMonth: _dayOfMonth,
        recurrence: _selectedRecurrence,
        description: _descriptionController.text.trim().isNotEmpty 
            ? _descriptionController.text.trim() 
            : null,
        type: _selectedType,
        reminderEnabled: _reminderEnabled,
        reminderDaysBefore: _reminderDaysBefore,
      );

      if (mounted) {
        Navigator.of(context).pop();
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Charge fixe enregistrée avec succès'),
            backgroundColor: Colors.green,
          ),
        );
        widget.onChargeAdded?.call();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Erreur: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }
}
