import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:form_validator/form_validator.dart';
import '../../../../core/providers/app_providers.dart';
import '../../../categories/domain/category.dart';
import '../../../../core/utils/currency_formatter.dart';
import '../../domain/budget.dart';

class BudgetsScreen extends ConsumerStatefulWidget {
  const BudgetsScreen({super.key});

  @override
  ConsumerState<BudgetsScreen> createState() => _BudgetsScreenState();
}

class _BudgetsScreenState extends ConsumerState<BudgetsScreen> {
  @override
  Widget build(BuildContext context) {
    final budgetService = ref.watch(budgetServiceProvider);
    final authService = ref.watch(authServiceProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Budgets'),
        elevation: 0,
        backgroundColor: Theme.of(context).colorScheme.primary,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: () {
              _showAddBudgetDialog(context);
            },
          ),
        ],
      ),
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
              Colors.white,
            ],
          ),
        ),
        child: FutureBuilder<List<Budget>>(
          future: budgetService.getBudgetsByUser(
            authService.currentUser?.id ?? '',
          ),
          builder: (context, snapshot) {
            if (snapshot.connectionState == ConnectionState.waiting) {
              return const Center(child: CircularProgressIndicator());
            }

            final budgets = snapshot.data ?? [];

            if (budgets.isEmpty) {
              return Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.account_balance_wallet_outlined,
                      size: 80,
                      color: Colors.grey[400],
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'Aucun budget créé',
                      style: TextStyle(
                        fontSize: 18,
                        color: Colors.grey[600],
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Créez votre premier budget pour\nmieux gérer vos finances',
                      textAlign: TextAlign.center,
                      style: TextStyle(color: Colors.grey[500], fontSize: 14),
                    ),
                    const SizedBox(height: 24),
                    ElevatedButton.icon(
                      onPressed: () => _showAddBudgetDialog(context),
                      icon: const Icon(Icons.add),
                      label: const Text('Créer un budget'),
                      style: ElevatedButton.styleFrom(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 24,
                          vertical: 12,
                        ),
                      ),
                    ),
                  ],
                ),
              );
            }

            return RefreshIndicator(
              onRefresh: () async {
                setState(() {});
              },
              child: ListView.builder(
                padding: const EdgeInsets.all(16),
                itemCount: budgets.length,
                itemBuilder: (context, index) {
                  final budget = budgets[index];
                  return _BudgetCard(
                    budget: budget,
                    onRefresh: () => setState(() {}),
                  );
                },
              ),
            );
          },
        ),
      ),
    );
  }

  void _showAddBudgetDialog(BuildContext context) {
    showDialog(
      context: context,
      builder:
          (context) => _AddBudgetDialog(onBudgetAdded: () => setState(() {})),
    );
  }
}

class _BudgetCard extends ConsumerWidget {
  final Budget budget;
  final VoidCallback onRefresh;

  const _BudgetCard({required this.budget, required this.onRefresh});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final budgetService = ref.watch(budgetServiceProvider);
    final transactionService = ref.watch(transactionServiceProvider);

    return FutureBuilder<int>(
      future: Future.value(
        transactionService.calculateTotalExpenses(
          budget.userId,
          startDate: budget.startDate,
          endDate: budget.endDate,
        ),
      ),
      builder: (context, snapshot) {
        final spentAmount = snapshot.data ?? 0;
        final percentage =
            budget.allocatedAmount > 0
                ? spentAmount.toDouble() / budget.allocatedAmount.toDouble()
                : 0.0;
        final remainingAmount = budget.allocatedAmount - spentAmount;

        return Card(
          margin: const EdgeInsets.only(bottom: 16),
          elevation: 4,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          child: Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(16),
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [Colors.white, Colors.grey[50]!],
              ),
            ),
            child: Padding(
              padding: const EdgeInsets.all(20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              budget.name,
                              style: const TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            const SizedBox(height: 4),
                            Text(
                              budget.categoryId,
                              style: TextStyle(
                                color: Colors.grey[600],
                                fontSize: 14,
                              ),
                            ),
                          ],
                        ),
                      ),
                      PopupMenuButton(
                        icon: const Icon(Icons.more_vert),
                        itemBuilder:
                            (context) => [
                              PopupMenuItem(
                                value: 'edit',
                                child: const Row(
                                  children: [
                                    Icon(Icons.edit, size: 20),
                                    SizedBox(width: 8),
                                    Text('Modifier'),
                                  ],
                                ),
                              ),
                              PopupMenuItem(
                                value: 'delete',
                                child: const Row(
                                  children: [
                                    Icon(
                                      Icons.delete,
                                      size: 20,
                                      color: Colors.red,
                                    ),
                                    SizedBox(width: 8),
                                    Text(
                                      'Supprimer',
                                      style: TextStyle(color: Colors.red),
                                    ),
                                  ],
                                ),
                              ),
                            ],
                        onSelected: (value) async {
                          if (value == 'edit') {
                            _showEditBudgetDialog(context, budget, ref);
                          } else if (value == 'delete') {
                            final confirmed = await _showDeleteConfirmation(
                              context,
                            );
                            if (confirmed) {
                              await budgetService.deleteBudget(budget.id);
                              onRefresh();
                            }
                          }
                        },
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        'Dépensé',
                        style: TextStyle(color: Colors.grey[600], fontSize: 14),
                      ),
                      Text(
                        'Budget total',
                        style: TextStyle(color: Colors.grey[600], fontSize: 14),
                      ),
                    ],
                  ),
                  const SizedBox(height: 4),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        CurrencyFormatter.formatAmount(spentAmount.toDouble()),
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color:
                              percentage > 0.9
                                  ? Colors.red[600]
                                  : Colors.black87,
                        ),
                      ),
                      Text(
                        CurrencyFormatter.formatAmount(
                          budget.allocatedAmount.toDouble() / 100.0,
                        ),
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  ClipRRect(
                    borderRadius: BorderRadius.circular(8),
                    child: LinearProgressIndicator(
                      value: percentage.clamp(0.0, 1.0),
                      backgroundColor: Colors.grey[200],
                      valueColor: AlwaysStoppedAnimation<Color>(
                        percentage > 0.9
                            ? Colors.red[600]!
                            : percentage > 0.7
                            ? Colors.orange[600]!
                            : Colors.green[600]!,
                      ),
                      minHeight: 12,
                    ),
                  ),
                  const SizedBox(height: 12),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        '${(percentage * 100).toStringAsFixed(1)}% utilisé',
                        style: TextStyle(color: Colors.grey[600], fontSize: 12),
                      ),
                      Text(
                        'Reste: ${CurrencyFormatter.formatCompact(remainingAmount.toDouble())}',
                        style: TextStyle(
                          color:
                              remainingAmount < 0
                                  ? Colors.red[600]
                                  : Colors.green[600],
                          fontSize: 12,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                  if (percentage > 1.0)
                    Container(
                      margin: const EdgeInsets.only(top: 12),
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: Colors.red[50],
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: Colors.red[200]!),
                      ),
                      child: Row(
                        children: [
                          Icon(Icons.warning, color: Colors.red[600], size: 16),
                          const SizedBox(width: 8),
                          Expanded(
                            child: Text(
                              'Budget dépassé de ${CurrencyFormatter.formatCompact((spentAmount - budget.allocatedAmount).toDouble())}',
                              style: TextStyle(
                                color: Colors.red[600],
                                fontSize: 12,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Future<bool> _showDeleteConfirmation(BuildContext context) async {
    return await showDialog<bool>(
          context: context,
          builder:
              (context) => AlertDialog(
                title: const Text('Supprimer le budget'),
                content: Text(
                  'Êtes-vous sûr de vouloir supprimer le budget "${budget.name}" ?',
                ),
                actions: [
                  TextButton(
                    onPressed: () => Navigator.of(context).pop(false),
                    child: const Text('Annuler'),
                  ),
                  ElevatedButton(
                    onPressed: () => Navigator.of(context).pop(true),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.red,
                      foregroundColor: Colors.white,
                    ),
                    child: const Text('Supprimer'),
                  ),
                ],
              ),
        ) ??
        false;
  }

  void _showEditBudgetDialog(
    BuildContext context,
    Budget budget,
    WidgetRef ref,
  ) {
    showDialog(
      context: context,
      builder:
          (context) =>
              _EditBudgetDialog(budget: budget, onBudgetUpdated: onRefresh),
    );
  }
}

class _AddBudgetDialog extends ConsumerStatefulWidget {
  final VoidCallback onBudgetAdded;

  const _AddBudgetDialog({required this.onBudgetAdded});

  @override
  ConsumerState<_AddBudgetDialog> createState() => _AddBudgetDialogState();
}

class _AddBudgetDialogState extends ConsumerState<_AddBudgetDialog> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _amountController = TextEditingController();
  final _categoryController = TextEditingController();
  bool _isLoading = false;

  List<String> _categories = [];
  bool _categoriesLoaded = false;
  String? _selectedCategory;

  @override
  void initState() {
    super.initState();
    _loadCategories();
  }

  Future<void> _loadCategories() async {
    try {
      final categoryService = ref.read(categoryServiceProvider);
      final categories = await categoryService.getCategoriesByType(
        CategoryType.expense,
      );
      setState(() {
        _categories = categories.map((c) => c.name).toList();
        _categoriesLoaded = true;
      });
    } catch (e) {
      // En cas d'erreur, utiliser des catégories par défaut
      setState(() {
        _categories = [
          'Alimentation',
          'Transport',
          'Logement',
          'Santé',
          'Éducation',
          'Loisirs',
          'Vêtements',
          'Autres',
        ];
        _categoriesLoaded = true;
      });
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _amountController.dispose();
    _categoryController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Créer un budget'),
      content: Form(
        key: _formKey,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextFormField(
              controller: _nameController,
              decoration: const InputDecoration(
                labelText: 'Nom du budget',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.label),
              ),
              validator:
                  ValidationBuilder().required('Le nom est requis').build(),
            ),
            const SizedBox(height: 16),
            _categoriesLoaded
                ? DropdownButtonFormField<String>(
                  value:
                      _categories.contains(_selectedCategory)
                          ? _selectedCategory
                          : null,
                  decoration: const InputDecoration(
                    labelText: 'Catégorie',
                    border: OutlineInputBorder(),
                    prefixIcon: Icon(Icons.category),
                  ),
                  items:
                      _categories.map((category) {
                        return DropdownMenuItem(
                          value: category,
                          child: Text(category),
                        );
                      }).toList(),
                  onChanged: (value) {
                    setState(() {
                      _selectedCategory = value;
                    });
                  },
                  validator:
                      (value) =>
                          value == null ? 'Sélectionnez une catégorie' : null,
                )
                : Container(
                  height: 56,
                  decoration: BoxDecoration(
                    border: Border.all(color: Colors.grey),
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: const Center(
                    child: Row(
                      children: [
                        SizedBox(width: 12),
                        Icon(Icons.category, color: Colors.grey),
                        SizedBox(width: 12),
                        Text(
                          'Chargement des catégories...',
                          style: TextStyle(color: Colors.grey),
                        ),
                        Spacer(),
                        SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(strokeWidth: 2),
                        ),
                        SizedBox(width: 12),
                      ],
                    ),
                  ),
                ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _amountController,
              decoration: const InputDecoration(
                labelText: 'Montant (FCFA)',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.attach_money),
              ),
              keyboardType: TextInputType.number,
              validator:
                  ValidationBuilder()
                      .required('Le montant est requis')
                      .regExp(RegExp(r'^[0-9]+$'), 'Montant invalide')
                      .build(),
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: _isLoading ? null : () => Navigator.pop(context),
          child: const Text('Annuler'),
        ),
        ElevatedButton(
          onPressed: _isLoading ? null : _createBudget,
          child:
              _isLoading
                  ? const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  )
                  : const Text('Créer'),
        ),
      ],
    );
  }

  Future<void> _createBudget() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final budgetService = ref.read(budgetServiceProvider);
      final authService = ref.read(authServiceProvider);
      final notificationService = ref.read(notificationServiceProvider);

      final amount = double.parse(_amountController.text);
      final now = DateTime.now();
      final startDate = DateTime(now.year, now.month, 1);
      final endDate = DateTime(now.year, now.month + 1, 0);

      final result = await budgetService.createBudget(
        userId: authService.currentUser?.id ?? '',
        name: _nameController.text.trim(),
        categoryId: _selectedCategory!,
        allocatedAmount: (amount * 100).toInt(), // Convert to cents
        startDate: startDate,
        endDate: endDate,
        alertThreshold: 80,
      );

      if (!result.success) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(result.message ?? 'Erreur lors de la création'),
            ),
          );
        }
        return;
      }

      await notificationService.showNotification(
        id: DateTime.now().millisecondsSinceEpoch,
        title: 'Succès',
        body: 'Budget créé avec succès',
      );
      widget.onBudgetAdded();

      if (mounted) {
        Navigator.pop(context);
      }
    } catch (e) {
      final notificationService = ref.read(notificationServiceProvider);
      await notificationService.showNotification(
        id: DateTime.now().millisecondsSinceEpoch,
        title: 'Erreur',
        body: 'Erreur lors de la création du budget',
      );
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }
}

class _EditBudgetDialog extends ConsumerStatefulWidget {
  final Budget budget;
  final VoidCallback onBudgetUpdated;

  const _EditBudgetDialog({
    required this.budget,
    required this.onBudgetUpdated,
  });

  @override
  ConsumerState<_EditBudgetDialog> createState() => _EditBudgetDialogState();
}

class _EditBudgetDialogState extends ConsumerState<_EditBudgetDialog> {
  final _formKey = GlobalKey<FormState>();
  late final TextEditingController _nameController;
  late final TextEditingController _amountController;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _nameController = TextEditingController(text: widget.budget.name);
    _amountController = TextEditingController(
      text: (widget.budget.allocatedAmount.toDouble() / 100.0).toString(),
    );
  }

  @override
  void dispose() {
    _nameController.dispose();
    _amountController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Modifier le budget'),
      content: Form(
        key: _formKey,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextFormField(
              controller: _nameController,
              decoration: const InputDecoration(
                labelText: 'Nom du budget',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.label),
              ),
              validator:
                  ValidationBuilder().required('Le nom est requis').build(),
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _amountController,
              decoration: const InputDecoration(
                labelText: 'Montant (FCFA)',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.attach_money),
              ),
              keyboardType: TextInputType.number,
              validator:
                  ValidationBuilder()
                      .required('Le montant est requis')
                      .regExp(RegExp(r'^[0-9]+$'), 'Montant invalide')
                      .build(),
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: _isLoading ? null : () => Navigator.pop(context),
          child: const Text('Annuler'),
        ),
        ElevatedButton(
          onPressed: _isLoading ? null : _updateBudget,
          child:
              _isLoading
                  ? const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  )
                  : const Text('Enregistrer'),
        ),
      ],
    );
  }

  Future<void> _updateBudget() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final budgetService = ref.read(budgetServiceProvider);
      final notificationService = ref.read(notificationServiceProvider);

      final amount = double.parse(_amountController.text);

      await budgetService.updateBudget(
        budgetId: widget.budget.id,
        name: _nameController.text.trim(),
        allocatedAmount: (amount * 100).round(),
      );

      await notificationService.showNotification(
        id: DateTime.now().millisecondsSinceEpoch,
        title: 'Succès',
        body: 'Budget mis à jour avec succès',
      );
      widget.onBudgetUpdated();

      if (mounted) {
        Navigator.pop(context);
      }
    } catch (e) {
      final notificationService = ref.read(notificationServiceProvider);
      await notificationService.showNotification(
        id: DateTime.now().millisecondsSinceEpoch,
        title: 'Erreur',
        body: 'Erreur lors de la mise à jour du budget',
      );
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }
}
