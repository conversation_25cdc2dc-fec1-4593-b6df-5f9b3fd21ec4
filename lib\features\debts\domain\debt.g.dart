// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'debt.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class DebtAdapter extends TypeAdapter<Debt> {
  @override
  final int typeId = 7;

  @override
  Debt read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return Debt(
      id: fields[0] as String,
      userId: fields[1] as String,
      creditorName: fields[2] as String,
      totalAmount: fields[3] as int,
      reason: fields[4] as String,
      type: fields[5] as DebtType,
      loanDate: fields[6] as DateTime,
      dueDate: fields[7] as DateTime?,
      repaymentType: fields[8] as RepaymentType,
      monthlyAmount: fields[9] as int?,
      attachmentPath: fields[10] as String?,
      status: fields[11] as DebtStatus,
      createdAt: fields[12] as DateTime,
      updatedAt: fields[13] as DateTime,
      notes: fields[14] as String?,
    );
  }

  @override
  void write(BinaryWriter writer, Debt obj) {
    writer
      ..writeByte(15)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.userId)
      ..writeByte(2)
      ..write(obj.creditorName)
      ..writeByte(3)
      ..write(obj.totalAmount)
      ..writeByte(4)
      ..write(obj.reason)
      ..writeByte(5)
      ..write(obj.type)
      ..writeByte(6)
      ..write(obj.loanDate)
      ..writeByte(7)
      ..write(obj.dueDate)
      ..writeByte(8)
      ..write(obj.repaymentType)
      ..writeByte(9)
      ..write(obj.monthlyAmount)
      ..writeByte(10)
      ..write(obj.attachmentPath)
      ..writeByte(11)
      ..write(obj.status)
      ..writeByte(12)
      ..write(obj.createdAt)
      ..writeByte(13)
      ..write(obj.updatedAt)
      ..writeByte(14)
      ..write(obj.notes);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is DebtAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class DebtTypeAdapter extends TypeAdapter<DebtType> {
  @override
  final int typeId = 8;

  @override
  DebtType read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return DebtType.personal;
      case 1:
        return DebtType.institutional;
      case 2:
        return DebtType.commercial;
      default:
        return DebtType.personal;
    }
  }

  @override
  void write(BinaryWriter writer, DebtType obj) {
    switch (obj) {
      case DebtType.personal:
        writer.writeByte(0);
        break;
      case DebtType.institutional:
        writer.writeByte(1);
        break;
      case DebtType.commercial:
        writer.writeByte(2);
        break;
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is DebtTypeAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class RepaymentTypeAdapter extends TypeAdapter<RepaymentType> {
  @override
  final int typeId = 9;

  @override
  RepaymentType read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return RepaymentType.single;
      case 1:
        return RepaymentType.monthly;
      case 2:
        return RepaymentType.weekly;
      case 3:
        return RepaymentType.biweekly;
      case 4:
        return RepaymentType.quarterly;
      default:
        return RepaymentType.single;
    }
  }

  @override
  void write(BinaryWriter writer, RepaymentType obj) {
    switch (obj) {
      case RepaymentType.single:
        writer.writeByte(0);
        break;
      case RepaymentType.monthly:
        writer.writeByte(1);
        break;
      case RepaymentType.weekly:
        writer.writeByte(2);
        break;
      case RepaymentType.biweekly:
        writer.writeByte(3);
        break;
      case RepaymentType.quarterly:
        writer.writeByte(4);
        break;
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is RepaymentTypeAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class DebtStatusAdapter extends TypeAdapter<DebtStatus> {
  @override
  final int typeId = 10;

  @override
  DebtStatus read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return DebtStatus.active;
      case 1:
        return DebtStatus.paid;
      case 2:
        return DebtStatus.overdue;
      case 3:
        return DebtStatus.suspended;
      default:
        return DebtStatus.active;
    }
  }

  @override
  void write(BinaryWriter writer, DebtStatus obj) {
    switch (obj) {
      case DebtStatus.active:
        writer.writeByte(0);
        break;
      case DebtStatus.paid:
        writer.writeByte(1);
        break;
      case DebtStatus.overdue:
        writer.writeByte(2);
        break;
      case DebtStatus.suspended:
        writer.writeByte(3);
        break;
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is DebtStatusAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
